{"configurations": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "includePath": ["/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/**", "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/variants/esp32s3/**", "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Wire/src/**", "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/SPI/src/**", "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WiFi/src/**", "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Network/src/**", "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WebServer/src/**", "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/FS/src/**", "/home/<USER>/Documents/Arduino/libraries/NTPClient/**", "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/SPIFFS/src/**", "/home/<USER>/Documents/Arduino/libraries/ArduinoJson/src/**", "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/DNSServer/src/**", "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/AsyncUDP/src/**", "/home/<USER>/Documents/Arduino/libraries/Adafruit_GFX_Library/**", "/home/<USER>/Documents/Arduino/libraries/Adafruit_BusIO/**", "/home/<USER>/Documents/Arduino/libraries/ESP32_HUB75_LED_MATRIX_PANEL_DMA_Display/src/**", "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/HTTPClient/src/**", "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/NetworkClientSecure/src/**", "/home/<USER>/Documents/Arduino/libraries/UnixTime/src/**", "/home/<USER>/Documents/Arduino/libraries/**", "/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/**", "/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/**"], "forcedInclude": ["/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/Arduino.h"], "compilerPath": "/home/<USER>/.arduino15/packages/esp32/tools/esp-x32/2411/bin/xtensa-esp32s3-elf-g++", "compilerArgs": ["/home/<USER>/.arduino15/packages/esp32/tools/esp-x32/2411/bin/xtensa-esp32s3-elf-g++", "-M<PERSON>", "-c", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/cpp_flags", "-w", "-<PERSON><PERSON>", "-Werror=return-type", "-DF_CPU=240000000L", "-DARDUINO=10607", "-DARDUINO_ESP32S3_DEV", "-DARDUINO_ARCH_ESP32", "-DARDUINO_BOARD=\"ESP32S3_DEV\"", "-DARDUINO_VARIANT=\"esp32s3\"", "-DARDUINO_PARTITION_default", "-DARDUINO_HOST_OS=\"linux\"", "-DARDUINO_FQBN=\"esp32:esp32:esp32s3:UploadSpeed=921600,USBMode=hwcdc,CDCOnBoot=default,MSCOnBoot=default,DFUOnBoot=default,UploadMode=default,CPUFreq=240,FlashMode=qio,FlashSize=4M,PartitionScheme=default,DebugLevel=none,PSRAM=disabled,LoopCore=1,EventsCore=1,EraseFlash=none,JTAGAdapter=default,ZigbeeMode=default\"", "-DESP32=ESP32", "-DCORE_DEBUG_LEVEL=0", "-DARDUINO_RUNNING_CORE=1", "-DARDUINO_EVENT_RUNNING_CORE=1", "-DARDUINO_USB_MODE=1", "-DARDUINO_USB_CDC_ON_BOOT=0", "-DARDUINO_USB_MSC_ON_BOOT=0", "-DARDUINO_USB_DFU_ON_BOOT=0", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/defines", "-I/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta", "-iprefix", "/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/includes", "-I/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/qio_qspi/include", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/variants/esp32s3", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Wire/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/SPI/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WiFi/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Network/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WebServer/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/FS/src", "-I/home/<USER>/Documents/Arduino/libraries/NTPClient", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/SPIFFS/src", "-I/home/<USER>/Documents/Arduino/libraries/ArduinoJson/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/DNSServer/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/AsyncUDP/src", "-I/home/<USER>/Documents/Arduino/libraries/Adafruit_GFX_Library", "-I/home/<USER>/Documents/Arduino/libraries/Adafruit_BusIO", "-I/home/<USER>/Documents/Arduino/libraries/ESP32_HUB75_LED_MATRIX_PANEL_DMA_Display/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/HTTPClient/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/NetworkClientSecure/src", "-I/home/<USER>/Documents/Arduino/libraries/UnixTime/src", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/build_opt.h", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/file_opts", "/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/sketch/esp32_vbz_eta.ino.cpp", "-o", "/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/sketch/esp32_vbz_eta.ino.cpp.o"], "defines": ["F_CPU=240000000L", "ARDUINO=10607", "ARDUINO_ESP32S3_DEV", "ARDUINO_ARCH_ESP32", "ARDUINO_BOARD=\"ESP32S3_DEV\"", "ARDUINO_VARIANT=\"esp32s3\"", "ARDUINO_PARTITION_default", "ARDUINO_HOST_OS=\"linux\"", "ARDUINO_FQBN=\"esp32:esp32:esp32s3:UploadSpeed=921600,USBMode=hwcdc,CDCOnBoot=default,MSCOnBoot=default,DFUOnBoot=default,UploadMode=default,CPUFreq=240,FlashMode=qio,FlashSize=4M,PartitionScheme=default,DebugLevel=none,PSRAM=disabled,LoopCore=1,EventsCore=1,EraseFlash=none,JTAGAdapter=default,ZigbeeMode=default\"", "ESP32=ESP32", "CORE_DEBUG_LEVEL=0", "ARDUINO_RUNNING_CORE=1", "ARDUINO_EVENT_RUNNING_CORE=1", "ARDUINO_USB_MODE=1", "ARDUINO_USB_CDC_ON_BOOT=0", "ARDUINO_USB_MSC_ON_BOOT=0", "ARDUINO_USB_DFU_ON_BOOT=0", "MA.h:", "MA.h)", "MA.cpp", "ARDUINO_BOARD=\\\"ESP32S3_DEV\\\"\"", "ARDUINO_VARIANT=\\\"esp32s3\\\"\"", "ARDUINO_HOST_OS=\\\"linux\\\"\"", "ARDUINO_FQBN=\\\"esp32:esp32:esp32s3:UploadSpeed=921600,USBMode=hwcdc,CDCOnBoot=default,MSCOnBoot=default,DFUOnBoot=default,UploadMode=default,CPUFreq=240,FlashMode=qio,FlashSize=4M,PartitionScheme=default,DebugLevel=none,PSRAM=disabled,LoopCore=1,EventsCore=1,EraseFlash=none,JTAGAdapter=default,ZigbeeMode=default\\\"\"", "MA.cpp.o", "ARDUINO_CORE_BUILD", "USBCON"], "cStandard": "c17", "cppStandard": "c++17"}], "version": 4}