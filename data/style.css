body {
    background-color: #121212;
    color: white;
    font-family: Arial, sans-serif;
    margin: 0;
    padding: 20px;
    display: flex;
    flex-direction: column;
    align-items: center;
    user-select: none;
}

.header {
    text-align: center;
    margin-bottom: 30px;
}

.header h1 {
    color: #ffff00;
    margin-bottom: 10px;
}

.header p {
    color: #aaa;
    margin: 5px 0;
}

.status-section, .controls-section, .config-section {
    background-color: #333;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 20px;
    width: 350px;
    text-align: center;
}

.status-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
    margin-bottom: 15px;
}

.status-item {
    background-color: #222;
    padding: 10px;
    border-radius: 5px;
}

.status-label {
    font-size: 12px;
    color: #aaa;
    margin-bottom: 5px;
}

.status-value {
    font-size: 16px;
    font-weight: bold;
}

.wifi-status { color: #00ff00; }
.api-status { color: #00ffff; }
.brightness { color: #ffff00; }
.update-time { color: #ff00ff; }

.control-group {
    margin-bottom: 20px;
}

.control-group h3 {
    color: #ffff00;
    margin-bottom: 10px;
    font-size: 16px;
}

.button-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
}

.btn {
    background-color: #444;
    border: none;
    color: white;
    padding: 12px;
    cursor: pointer;
    border-radius: 5px;
    transition: background-color 0.1s;
    touch-action: manipulation;
    font-size: 14px;
    text-decoration: none;
    display: inline-block;
    text-align: center;
}

.btn:hover {
    background-color: #555;
}

.btn:active {
    background-color: #00ff00;
    color: black;
}

.btn.primary {
    background-color: #0066cc;
}

.btn.primary:hover {
    background-color: #0088ff;
}

.btn.success {
    background-color: #006600;
}

.btn.success:hover {
    background-color: #008800;
}

.form-group {
    margin-bottom: 15px;
    text-align: left;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    color: #aaa;
    font-size: 14px;
}

.form-group input, .form-group select {
    width: 100%;
    background-color: #222;
    border: 1px solid #444;
    color: white;
    padding: 10px;
    border-radius: 5px;
    font-size: 14px;
    box-sizing: border-box;
}

.form-group input:focus, .form-group select:focus {
    outline: none;
    border-color: #ffff00;
}

.connection-status {
    margin-top: 20px;
    padding: 10px;
    border-radius: 5px;
    text-align: center;
    width: 350px;
}

.connected {
    background-color: #004400;
    color: #00ff00;
}

.disconnected {
    background-color: #440000;
    color: #ff4444;
}

.info-section {
    margin-top: 20px;
    font-size: 14px;
    color: #aaa;
    text-align: center;
    max-width: 350px;
    background-color: #333;
    padding: 15px;
    border-radius: 10px;
}

.full-width {
    grid-column: span 2;
}

.text-center {
    text-align: center;
}

.mt-20 {
    margin-top: 20px;
}

.mb-20 {
    margin-bottom: 20px;
}

.loading {
    color: #ffff00;
    font-style: italic;
}

.error {
    color: #ff4444;
    background-color: #440000;
    padding: 10px;
    border-radius: 5px;
    margin: 10px 0;
}

.success {
    color: #00ff00;
    background-color: #004400;
    padding: 10px;
    border-radius: 5px;
    margin: 10px 0;
}

.nav-links {
    margin-top: 20px;
    text-align: center;
}

.nav-links a {
    color: #00ffff;
    text-decoration: none;
    margin: 0 10px;
    font-size: 14px;
}

.nav-links a:hover {
    color: #ffff00;
}

/* Responsive design */
@media (max-width: 480px) {
    body {
        padding: 10px;
    }
    
    .status-section, .controls-section, .config-section, .connection-status {
        width: calc(100vw - 40px);
        max-width: 350px;
    }
    
    .button-grid {
        grid-template-columns: 1fr;
    }
}
