<!DOCTYPE html>
<html>
<head>
    <title>VBZ Display Setup</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="/style.css">
</head>
<body>
    <div class="header">
        <h1>🚋 VBZ Display Setup</h1>
        <p>Welcome to the VBZ Display configuration portal</p>
    </div>

    <div class="status-section">
        <h3 style="color: #ffff00; margin-bottom: 15px;">Device Status</h3>
        <div class="status-grid">
            <div class="status-item">
                <div class="status-label">MODE</div>
                <div class="status-value" style="color: #ff8800;">Access Point</div>
            </div>
            <div class="status-item">
                <div class="status-label">AP NAME</div>
                <div class="status-value wifi-status">vbz-anzeige</div>
            </div>
            <div class="status-item">
                <div class="status-label">AP PASSWORD</div>
                <div class="status-value brightness">123456</div>
            </div>
            <div class="status-item">
                <div class="status-label">IP ADDRESS</div>
                <div class="status-value api-status" id="ap-ip">***********</div>
            </div>
        </div>
    </div>

    <div class="controls-section">
        <div class="control-group">
            <h3>🌐 WiFi Configuration</h3>
            <p style="color: #aaa; font-size: 14px; margin-bottom: 15px;">
                Connect your VBZ Display to your WiFi network for internet access and remote control.
            </p>
            <a href="/wifi-config" class="btn primary full-width">Configure WiFi Connection</a>
        </div>

        <div class="control-group">
            <h3>🎛️ Display Controls</h3>
            <div class="button-grid">
                <button class="btn" onclick="sendCommand('refresh')">🔄 Refresh Data</button>
                <button class="btn" onclick="sendCommand('toggle')">💡 Toggle Display</button>
                <button class="btn" onclick="sendCommand('show-ip')">🌐 Show IP</button>
                <button class="btn" onclick="sendCommand('splash')">🏠 Show Splash</button>
            </div>
        </div>

        <div class="control-group">
            <h3>📊 System Information</h3>
            <div class="button-grid">
                <a href="/status" class="btn">📈 View Status</a>
                <button class="btn" onclick="location.reload()">🔄 Refresh Page</button>
            </div>
        </div>
    </div>

    <div class="info-section">
        <strong>🚋 VBZ ETA Display</strong><br>
        Real-time departure information for Zürich public transport<br><br>
        
        <strong>Setup Steps:</strong><br>
        1. Configure WiFi connection above<br>
        2. Device will connect to your network<br>
        3. Access the full control interface<br>
        4. Configure station and display settings<br><br>
        
        <strong>Features:</strong><br>
        • Live departure times with delay information<br>
        • Accessibility indicators for wheelchair access<br>
        • Automatic brightness adjustment<br>
        • Configurable station and direction filtering<br>
        • Web-based remote control interface
    </div>

    <div class="connection-status disconnected" id="connection-status">
        Setup Mode - Configure WiFi to continue
    </div>

    <script>
        // Update AP IP address
        fetch('/status')
            .then(response => response.json())
            .then(data => {
                // Update any dynamic information if available
                console.log('Status:', data);
            })
            .catch(error => {
                console.log('Status not available in AP mode');
            });

        // Send command to ESP32
        async function sendCommand(endpoint) {
            try {
                const response = await fetch('/' + endpoint);
                
                if (response.ok) {
                    const result = await response.text();
                    
                    // Show feedback
                    const status = document.getElementById('connection-status');
                    status.className = 'connection-status connected';
                    status.textContent = 'Command sent: ' + endpoint;
                    
                    // Reset status after 3 seconds
                    setTimeout(() => {
                        status.className = 'connection-status disconnected';
                        status.textContent = 'Setup Mode - Configure WiFi to continue';
                    }, 3000);
                    
                } else {
                    throw new Error(`Command failed: ${response.status}`);
                }
            } catch (error) {
                console.error('Error:', error);
                
                const status = document.getElementById('connection-status');
                status.className = 'connection-status disconnected';
                status.textContent = 'Command failed - Check connection';
                
                setTimeout(() => {
                    status.textContent = 'Setup Mode - Configure WiFi to continue';
                }, 3000);
            }
        }

        // Add touch events for mobile
        function addTouchEvents(element, handler) {
            if (element) {
                element.addEventListener('touchstart', (e) => {
                    e.preventDefault();
                    handler();
                });
            }
        }

        // Add touch events for all command buttons
        document.querySelectorAll('button[onclick*="sendCommand"]').forEach(btn => {
            const command = btn.getAttribute('onclick').match(/sendCommand\('(.+?)'\)/)[1];
            addTouchEvents(btn, () => sendCommand(command));
        });

        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            switch(e.key.toLowerCase()) {
                case 'w':
                    e.preventDefault();
                    window.location.href = '/wifi-config';
                    break;
                case 'r':
                    if (e.ctrlKey || e.metaKey) return;
                    e.preventDefault();
                    sendCommand('refresh');
                    break;
                case 't':
                    e.preventDefault();
                    sendCommand('toggle');
                    break;
                case 'i':
                    e.preventDefault();
                    sendCommand('show-ip');
                    break;
                case 's':
                    e.preventDefault();
                    sendCommand('splash');
                    break;
            }
        });
    </script>
</body>
</html>
