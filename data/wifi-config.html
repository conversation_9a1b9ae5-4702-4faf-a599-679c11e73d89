<!DOCTYPE html>
<html>
<head>
    <title>WiFi Configuration - VBZ Display</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="/style.css">
</head>
<body>
    <div class="header">
        <h1>🌐 WiFi Configuration</h1>
        <p>Configure your WiFi connection</p>
    </div>

    <div class="config-section">
        <form action="/wifi-save" method="post" id="wifi-form">
            <div class="form-group">
                <label for="ssid">WiFi Network Name (SSID)</label>
                <input type="text" id="ssid" name="ssid" required placeholder="Enter your WiFi network name">
            </div>
            
            <div class="form-group">
                <label for="password">WiFi Password</label>
                <input type="password" id="password" name="password" placeholder="Enter your WiFi password">
            </div>
            
            <div class="form-group">
                <button type="submit" class="btn primary full-width">💾 Save and Connect</button>
            </div>
        </form>
        
        <div class="form-group">
            <button type="button" class="btn" id="scan-networks">🔍 Scan for Networks</button>
        </div>
        
        <div id="network-list" class="mt-20" style="display: none;">
            <h3 style="color: #ffff00;">Available Networks:</h3>
            <div id="networks"></div>
        </div>
    </div>

    <div class="info-section">
        <strong>WiFi Setup Instructions:</strong><br>
        1. Enter your WiFi network name and password<br>
        2. Click "Save and Connect" to attempt connection<br>
        3. The device will restart and try to connect<br>
        4. If connection fails, it will return to AP mode<br><br>
        
        <strong>Current Status:</strong><br>
        Mode: Access Point<br>
        AP Name: <span style="color: #ffff00;">vbz-anzeige</span><br>
        AP Password: <span style="color: #ffff00;">123456</span>
    </div>

    <div class="nav-links">
        <a href="/">← Back to Main Page</a> |
        <a href="/status">📊 View Status</a>
    </div>

    <script>
        // Handle form submission
        document.getElementById('wifi-form').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const ssid = document.getElementById('ssid').value;
            const password = document.getElementById('password').value;
            
            if (!ssid.trim()) {
                alert('Please enter a WiFi network name');
                return;
            }
            
            // Show loading state
            const submitBtn = document.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '⏳ Connecting...';
            submitBtn.disabled = true;
            
            // Submit the form
            fetch('/wifi-save', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `ssid=${encodeURIComponent(ssid)}&password=${encodeURIComponent(password)}`
            })
            .then(response => response.text())
            .then(data => {
                // Show success message
                alert('WiFi configuration saved! The device is attempting to connect. Please wait...');
                
                // Redirect after a delay
                setTimeout(() => {
                    window.location.href = '/';
                }, 5000);
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Failed to save WiFi configuration. Please try again.');
                
                // Restore button
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            });
        });

        // Network scanning (placeholder - would need ESP32 implementation)
        document.getElementById('scan-networks').addEventListener('click', function() {
            const btn = this;
            const originalText = btn.innerHTML;
            btn.innerHTML = '⏳ Scanning...';
            btn.disabled = true;
            
            // Simulate network scan (in real implementation, this would call ESP32 endpoint)
            setTimeout(() => {
                const networkList = document.getElementById('network-list');
                const networks = document.getElementById('networks');
                
                // Example networks (in real implementation, get from ESP32)
                const exampleNetworks = [
                    { ssid: 'Home_WiFi', rssi: -45, secure: true },
                    { ssid: 'Office_Network', rssi: -60, secure: true },
                    { ssid: 'Guest_WiFi', rssi: -75, secure: false }
                ];
                
                networks.innerHTML = exampleNetworks.map(network => `
                    <div style="background-color: #222; padding: 10px; margin: 5px 0; border-radius: 5px; cursor: pointer;" 
                         onclick="selectNetwork('${network.ssid}')">
                        <strong>${network.ssid}</strong> 
                        <span style="color: #aaa;">(${network.rssi} dBm)</span>
                        ${network.secure ? '🔒' : '🔓'}
                    </div>
                `).join('');
                
                networkList.style.display = 'block';
                
                btn.innerHTML = originalText;
                btn.disabled = false;
            }, 2000);
        });

        function selectNetwork(ssid) {
            document.getElementById('ssid').value = ssid;
        }
    </script>
</body>
</html>
