/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/core/MacAddress.cpp.o: \
 /home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/MacAddress.cpp \
 /home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/MacAddress.h \
 /home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/WString.h \
 /home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/pgmspace.h \
 /home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/Printable.h \
 /home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/Print.h \
 /home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/WString.h \
 /home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/Printable.h
