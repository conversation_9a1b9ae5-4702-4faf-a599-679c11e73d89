[{"directory": "/home/<USER>", "arguments": ["/home/<USER>/.arduino15/packages/esp32/tools/esp-x32/2411/bin/xtensa-esp32s3-elf-g++", "-M<PERSON>", "-c", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/cpp_flags", "-w", "-<PERSON><PERSON>", "-Werror=return-type", "-DF_CPU=240000000L", "-DARDUINO=10607", "-DARDUINO_ESP32S3_DEV", "-DARDUINO_ARCH_ESP32", "-DARDUINO_BOARD=\"ESP32S3_DEV\"", "-DARDUINO_VARIANT=\"esp32s3\"", "-DARDUINO_PARTITION_default", "-DARDUINO_HOST_OS=\"linux\"", "-DARDUINO_FQBN=\"esp32:esp32:esp32s3:UploadSpeed=921600,USBMode=hwcdc,CDCOnBoot=default,MSCOnBoot=default,DFUOnBoot=default,UploadMode=default,CPUFreq=240,FlashMode=qio,FlashSize=4M,PartitionScheme=default,DebugLevel=none,PSRAM=disabled,LoopCore=1,EventsCore=1,EraseFlash=none,JTAGAdapter=default,ZigbeeMode=default\"", "-DESP32=ESP32", "-DCORE_DEBUG_LEVEL=0", "-DARDUINO_RUNNING_CORE=1", "-DARDUINO_EVENT_RUNNING_CORE=1", "-DARDUINO_USB_MODE=1", "-DARDUINO_USB_CDC_ON_BOOT=0", "-DARDUINO_USB_MSC_ON_BOOT=0", "-DARDUINO_USB_DFU_ON_BOOT=0", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/defines", "-I/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta", "-iprefix", "/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/includes", "-I/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/qio_qspi/include", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/variants/esp32s3", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Wire/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/SPI/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WiFi/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Network/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WebServer/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/FS/src", "-I/home/<USER>/Documents/Arduino/libraries/NTPClient", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/SPIFFS/src", "-I/home/<USER>/Documents/Arduino/libraries/ArduinoJson/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/DNSServer/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/AsyncUDP/src", "-I/home/<USER>/Documents/Arduino/libraries/Adafruit_GFX_Library", "-I/home/<USER>/Documents/Arduino/libraries/Adafruit_BusIO", "-I/home/<USER>/Documents/Arduino/libraries/ESP32_HUB75_LED_MATRIX_PANEL_DMA_Display/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/HTTPClient/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/NetworkClientSecure/src", "-I/home/<USER>/Documents/Arduino/libraries/UnixTime/src", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/build_opt.h", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/file_opts", "/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/sketch/esp32_vbz_eta.ino.cpp", "-o", "/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/sketch/esp32_vbz_eta.ino.cpp.o"], "file": "/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/sketch/esp32_vbz_eta.ino.cpp"}, {"directory": "/home/<USER>", "arguments": ["/home/<USER>/.arduino15/packages/esp32/tools/esp-x32/2411/bin/xtensa-esp32s3-elf-g++", "-M<PERSON>", "-c", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/cpp_flags", "-w", "-<PERSON><PERSON>", "-Werror=return-type", "-DF_CPU=240000000L", "-DARDUINO=10607", "-DARDUINO_ESP32S3_DEV", "-DARDUINO_ARCH_ESP32", "-DARDUINO_BOARD=\"ESP32S3_DEV\"", "-DARDUINO_VARIANT=\"esp32s3\"", "-DARDUINO_PARTITION_default", "-DARDUINO_HOST_OS=\"linux\"", "-DARDUINO_FQBN=\"esp32:esp32:esp32s3:UploadSpeed=921600,USBMode=hwcdc,CDCOnBoot=default,MSCOnBoot=default,DFUOnBoot=default,UploadMode=default,CPUFreq=240,FlashMode=qio,FlashSize=4M,PartitionScheme=default,DebugLevel=none,PSRAM=disabled,LoopCore=1,EventsCore=1,EraseFlash=none,JTAGAdapter=default,ZigbeeMode=default\"", "-DESP32=ESP32", "-DCORE_DEBUG_LEVEL=0", "-DARDUINO_RUNNING_CORE=1", "-DARDUINO_EVENT_RUNNING_CORE=1", "-DARDUINO_USB_MODE=1", "-DARDUINO_USB_CDC_ON_BOOT=0", "-DARDUINO_USB_MSC_ON_BOOT=0", "-DARDUINO_USB_DFU_ON_BOOT=0", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/defines", "-I/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta", "-iprefix", "/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/includes", "-I/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/qio_qspi/include", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/variants/esp32s3", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Wire/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/SPI/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WiFi/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Network/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WebServer/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/FS/src", "-I/home/<USER>/Documents/Arduino/libraries/NTPClient", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/SPIFFS/src", "-I/home/<USER>/Documents/Arduino/libraries/ArduinoJson/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/DNSServer/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/AsyncUDP/src", "-I/home/<USER>/Documents/Arduino/libraries/Adafruit_GFX_Library", "-I/home/<USER>/Documents/Arduino/libraries/Adafruit_BusIO", "-I/home/<USER>/Documents/Arduino/libraries/ESP32_HUB75_LED_MATRIX_PANEL_DMA_Display/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/HTTPClient/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/NetworkClientSecure/src", "-I/home/<USER>/Documents/Arduino/libraries/UnixTime/src", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/build_opt.h", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/file_opts", "/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/sketch/Display.cpp", "-o", "/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/sketch/Display.cpp.o"], "file": "/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/sketch/Display.cpp"}, {"directory": "/home/<USER>", "arguments": ["/home/<USER>/.arduino15/packages/esp32/tools/esp-x32/2411/bin/xtensa-esp32s3-elf-g++", "-M<PERSON>", "-c", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/cpp_flags", "-w", "-<PERSON><PERSON>", "-Werror=return-type", "-DF_CPU=240000000L", "-DARDUINO=10607", "-DARDUINO_ESP32S3_DEV", "-DARDUINO_ARCH_ESP32", "-DARDUINO_BOARD=\"ESP32S3_DEV\"", "-DARDUINO_VARIANT=\"esp32s3\"", "-DARDUINO_PARTITION_default", "-DARDUINO_HOST_OS=\"linux\"", "-DARDUINO_FQBN=\"esp32:esp32:esp32s3:UploadSpeed=921600,USBMode=hwcdc,CDCOnBoot=default,MSCOnBoot=default,DFUOnBoot=default,UploadMode=default,CPUFreq=240,FlashMode=qio,FlashSize=4M,PartitionScheme=default,DebugLevel=none,PSRAM=disabled,LoopCore=1,EventsCore=1,EraseFlash=none,JTAGAdapter=default,ZigbeeMode=default\"", "-DESP32=ESP32", "-DCORE_DEBUG_LEVEL=0", "-DARDUINO_RUNNING_CORE=1", "-DARDUINO_EVENT_RUNNING_CORE=1", "-DARDUINO_USB_MODE=1", "-DARDUINO_USB_CDC_ON_BOOT=0", "-DARDUINO_USB_MSC_ON_BOOT=0", "-DARDUINO_USB_DFU_ON_BOOT=0", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/defines", "-I/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta", "-iprefix", "/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/includes", "-I/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/qio_qspi/include", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/variants/esp32s3", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Wire/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/SPI/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WiFi/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Network/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WebServer/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/FS/src", "-I/home/<USER>/Documents/Arduino/libraries/NTPClient", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/SPIFFS/src", "-I/home/<USER>/Documents/Arduino/libraries/ArduinoJson/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/DNSServer/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/AsyncUDP/src", "-I/home/<USER>/Documents/Arduino/libraries/Adafruit_GFX_Library", "-I/home/<USER>/Documents/Arduino/libraries/Adafruit_BusIO", "-I/home/<USER>/Documents/Arduino/libraries/ESP32_HUB75_LED_MATRIX_PANEL_DMA_Display/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/HTTPClient/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/NetworkClientSecure/src", "-I/home/<USER>/Documents/Arduino/libraries/UnixTime/src", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/build_opt.h", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/file_opts", "/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/sketch/OpenTransportDataSwiss.cpp", "-o", "/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/sketch/OpenTransportDataSwiss.cpp.o"], "file": "/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/sketch/OpenTransportDataSwiss.cpp"}, {"directory": "/home/<USER>", "arguments": ["/home/<USER>/.arduino15/packages/esp32/tools/esp-x32/2411/bin/xtensa-esp32s3-elf-g++", "-M<PERSON>", "-c", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/cpp_flags", "-w", "-<PERSON><PERSON>", "-Werror=return-type", "-DF_CPU=240000000L", "-DARDUINO=10607", "-DARDUINO_ESP32S3_DEV", "-DARDUINO_ARCH_ESP32", "-DARDUINO_BOARD=\"ESP32S3_DEV\"", "-DARDUINO_VARIANT=\"esp32s3\"", "-DARDUINO_PARTITION_default", "-DARDUINO_HOST_OS=\"linux\"", "-DARDUINO_FQBN=\"esp32:esp32:esp32s3:UploadSpeed=921600,USBMode=hwcdc,CDCOnBoot=default,MSCOnBoot=default,DFUOnBoot=default,UploadMode=default,CPUFreq=240,FlashMode=qio,FlashSize=4M,PartitionScheme=default,DebugLevel=none,PSRAM=disabled,LoopCore=1,EventsCore=1,EraseFlash=none,JTAGAdapter=default,ZigbeeMode=default\"", "-DESP32=ESP32", "-DCORE_DEBUG_LEVEL=0", "-DARDUINO_RUNNING_CORE=1", "-DARDUINO_EVENT_RUNNING_CORE=1", "-DARDUINO_USB_MODE=1", "-DARDUINO_USB_CDC_ON_BOOT=0", "-DARDUINO_USB_MSC_ON_BOOT=0", "-DARDUINO_USB_DFU_ON_BOOT=0", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/defines", "-I/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta", "-iprefix", "/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/includes", "-I/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/qio_qspi/include", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/variants/esp32s3", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Wire/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/SPI/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WiFi/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Network/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WebServer/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/FS/src", "-I/home/<USER>/Documents/Arduino/libraries/NTPClient", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/SPIFFS/src", "-I/home/<USER>/Documents/Arduino/libraries/ArduinoJson/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/DNSServer/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/AsyncUDP/src", "-I/home/<USER>/Documents/Arduino/libraries/Adafruit_GFX_Library", "-I/home/<USER>/Documents/Arduino/libraries/Adafruit_BusIO", "-I/home/<USER>/Documents/Arduino/libraries/ESP32_HUB75_LED_MATRIX_PANEL_DMA_Display/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/HTTPClient/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/NetworkClientSecure/src", "-I/home/<USER>/Documents/Arduino/libraries/UnixTime/src", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/build_opt.h", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/file_opts", "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Wire/src/Wire.cpp", "-o", "/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/libraries/Wire/Wire.cpp.o"], "file": "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Wire/src/Wire.cpp"}, {"directory": "/home/<USER>", "arguments": ["/home/<USER>/.arduino15/packages/esp32/tools/esp-x32/2411/bin/xtensa-esp32s3-elf-g++", "-M<PERSON>", "-c", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/cpp_flags", "-w", "-<PERSON><PERSON>", "-Werror=return-type", "-DF_CPU=240000000L", "-DARDUINO=10607", "-DARDUINO_ESP32S3_DEV", "-DARDUINO_ARCH_ESP32", "-DARDUINO_BOARD=\"ESP32S3_DEV\"", "-DARDUINO_VARIANT=\"esp32s3\"", "-DARDUINO_PARTITION_default", "-DARDUINO_HOST_OS=\"linux\"", "-DARDUINO_FQBN=\"esp32:esp32:esp32s3:UploadSpeed=921600,USBMode=hwcdc,CDCOnBoot=default,MSCOnBoot=default,DFUOnBoot=default,UploadMode=default,CPUFreq=240,FlashMode=qio,FlashSize=4M,PartitionScheme=default,DebugLevel=none,PSRAM=disabled,LoopCore=1,EventsCore=1,EraseFlash=none,JTAGAdapter=default,ZigbeeMode=default\"", "-DESP32=ESP32", "-DCORE_DEBUG_LEVEL=0", "-DARDUINO_RUNNING_CORE=1", "-DARDUINO_EVENT_RUNNING_CORE=1", "-DARDUINO_USB_MODE=1", "-DARDUINO_USB_CDC_ON_BOOT=0", "-DARDUINO_USB_MSC_ON_BOOT=0", "-DARDUINO_USB_DFU_ON_BOOT=0", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/defines", "-I/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta", "-iprefix", "/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/includes", "-I/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/qio_qspi/include", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/variants/esp32s3", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Wire/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/SPI/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WiFi/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Network/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WebServer/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/FS/src", "-I/home/<USER>/Documents/Arduino/libraries/NTPClient", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/SPIFFS/src", "-I/home/<USER>/Documents/Arduino/libraries/ArduinoJson/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/DNSServer/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/AsyncUDP/src", "-I/home/<USER>/Documents/Arduino/libraries/Adafruit_GFX_Library", "-I/home/<USER>/Documents/Arduino/libraries/Adafruit_BusIO", "-I/home/<USER>/Documents/Arduino/libraries/ESP32_HUB75_LED_MATRIX_PANEL_DMA_Display/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/HTTPClient/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/NetworkClientSecure/src", "-I/home/<USER>/Documents/Arduino/libraries/UnixTime/src", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/build_opt.h", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/file_opts", "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/SPI/src/SPI.cpp", "-o", "/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/libraries/SPI/SPI.cpp.o"], "file": "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/SPI/src/SPI.cpp"}, {"directory": "/home/<USER>", "arguments": ["/home/<USER>/.arduino15/packages/esp32/tools/esp-x32/2411/bin/xtensa-esp32s3-elf-g++", "-M<PERSON>", "-c", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/cpp_flags", "-w", "-<PERSON><PERSON>", "-Werror=return-type", "-DF_CPU=240000000L", "-DARDUINO=10607", "-DARDUINO_ESP32S3_DEV", "-DARDUINO_ARCH_ESP32", "-DARDUINO_BOARD=\"ESP32S3_DEV\"", "-DARDUINO_VARIANT=\"esp32s3\"", "-DARDUINO_PARTITION_default", "-DARDUINO_HOST_OS=\"linux\"", "-DARDUINO_FQBN=\"esp32:esp32:esp32s3:UploadSpeed=921600,USBMode=hwcdc,CDCOnBoot=default,MSCOnBoot=default,DFUOnBoot=default,UploadMode=default,CPUFreq=240,FlashMode=qio,FlashSize=4M,PartitionScheme=default,DebugLevel=none,PSRAM=disabled,LoopCore=1,EventsCore=1,EraseFlash=none,JTAGAdapter=default,ZigbeeMode=default\"", "-DESP32=ESP32", "-DCORE_DEBUG_LEVEL=0", "-DARDUINO_RUNNING_CORE=1", "-DARDUINO_EVENT_RUNNING_CORE=1", "-DARDUINO_USB_MODE=1", "-DARDUINO_USB_CDC_ON_BOOT=0", "-DARDUINO_USB_MSC_ON_BOOT=0", "-DARDUINO_USB_DFU_ON_BOOT=0", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/defines", "-I/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta", "-iprefix", "/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/includes", "-I/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/qio_qspi/include", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/variants/esp32s3", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Wire/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/SPI/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WiFi/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Network/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WebServer/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/FS/src", "-I/home/<USER>/Documents/Arduino/libraries/NTPClient", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/SPIFFS/src", "-I/home/<USER>/Documents/Arduino/libraries/ArduinoJson/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/DNSServer/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/AsyncUDP/src", "-I/home/<USER>/Documents/Arduino/libraries/Adafruit_GFX_Library", "-I/home/<USER>/Documents/Arduino/libraries/Adafruit_BusIO", "-I/home/<USER>/Documents/Arduino/libraries/ESP32_HUB75_LED_MATRIX_PANEL_DMA_Display/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/HTTPClient/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/NetworkClientSecure/src", "-I/home/<USER>/Documents/Arduino/libraries/UnixTime/src", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/build_opt.h", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/file_opts", "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WiFi/src/WiFiAP.cpp", "-o", "/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/libraries/WiFi/WiFiAP.cpp.o"], "file": "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WiFi/src/WiFiAP.cpp"}, {"directory": "/home/<USER>", "arguments": ["/home/<USER>/.arduino15/packages/esp32/tools/esp-x32/2411/bin/xtensa-esp32s3-elf-g++", "-M<PERSON>", "-c", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/cpp_flags", "-w", "-<PERSON><PERSON>", "-Werror=return-type", "-DF_CPU=240000000L", "-DARDUINO=10607", "-DARDUINO_ESP32S3_DEV", "-DARDUINO_ARCH_ESP32", "-DARDUINO_BOARD=\"ESP32S3_DEV\"", "-DARDUINO_VARIANT=\"esp32s3\"", "-DARDUINO_PARTITION_default", "-DARDUINO_HOST_OS=\"linux\"", "-DARDUINO_FQBN=\"esp32:esp32:esp32s3:UploadSpeed=921600,USBMode=hwcdc,CDCOnBoot=default,MSCOnBoot=default,DFUOnBoot=default,UploadMode=default,CPUFreq=240,FlashMode=qio,FlashSize=4M,PartitionScheme=default,DebugLevel=none,PSRAM=disabled,LoopCore=1,EventsCore=1,EraseFlash=none,JTAGAdapter=default,ZigbeeMode=default\"", "-DESP32=ESP32", "-DCORE_DEBUG_LEVEL=0", "-DARDUINO_RUNNING_CORE=1", "-DARDUINO_EVENT_RUNNING_CORE=1", "-DARDUINO_USB_MODE=1", "-DARDUINO_USB_CDC_ON_BOOT=0", "-DARDUINO_USB_MSC_ON_BOOT=0", "-DARDUINO_USB_DFU_ON_BOOT=0", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/defines", "-I/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta", "-iprefix", "/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/includes", "-I/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/qio_qspi/include", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/variants/esp32s3", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Wire/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/SPI/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WiFi/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Network/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WebServer/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/FS/src", "-I/home/<USER>/Documents/Arduino/libraries/NTPClient", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/SPIFFS/src", "-I/home/<USER>/Documents/Arduino/libraries/ArduinoJson/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/DNSServer/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/AsyncUDP/src", "-I/home/<USER>/Documents/Arduino/libraries/Adafruit_GFX_Library", "-I/home/<USER>/Documents/Arduino/libraries/Adafruit_BusIO", "-I/home/<USER>/Documents/Arduino/libraries/ESP32_HUB75_LED_MATRIX_PANEL_DMA_Display/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/HTTPClient/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/NetworkClientSecure/src", "-I/home/<USER>/Documents/Arduino/libraries/UnixTime/src", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/build_opt.h", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/file_opts", "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WiFi/src/WiFiScan.cpp", "-o", "/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/libraries/WiFi/WiFiScan.cpp.o"], "file": "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WiFi/src/WiFiScan.cpp"}, {"directory": "/home/<USER>", "arguments": ["/home/<USER>/.arduino15/packages/esp32/tools/esp-x32/2411/bin/xtensa-esp32s3-elf-g++", "-M<PERSON>", "-c", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/cpp_flags", "-w", "-<PERSON><PERSON>", "-Werror=return-type", "-DF_CPU=240000000L", "-DARDUINO=10607", "-DARDUINO_ESP32S3_DEV", "-DARDUINO_ARCH_ESP32", "-DARDUINO_BOARD=\"ESP32S3_DEV\"", "-DARDUINO_VARIANT=\"esp32s3\"", "-DARDUINO_PARTITION_default", "-DARDUINO_HOST_OS=\"linux\"", "-DARDUINO_FQBN=\"esp32:esp32:esp32s3:UploadSpeed=921600,USBMode=hwcdc,CDCOnBoot=default,MSCOnBoot=default,DFUOnBoot=default,UploadMode=default,CPUFreq=240,FlashMode=qio,FlashSize=4M,PartitionScheme=default,DebugLevel=none,PSRAM=disabled,LoopCore=1,EventsCore=1,EraseFlash=none,JTAGAdapter=default,ZigbeeMode=default\"", "-DESP32=ESP32", "-DCORE_DEBUG_LEVEL=0", "-DARDUINO_RUNNING_CORE=1", "-DARDUINO_EVENT_RUNNING_CORE=1", "-DARDUINO_USB_MODE=1", "-DARDUINO_USB_CDC_ON_BOOT=0", "-DARDUINO_USB_MSC_ON_BOOT=0", "-DARDUINO_USB_DFU_ON_BOOT=0", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/defines", "-I/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta", "-iprefix", "/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/includes", "-I/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/qio_qspi/include", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/variants/esp32s3", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Wire/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/SPI/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WiFi/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Network/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WebServer/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/FS/src", "-I/home/<USER>/Documents/Arduino/libraries/NTPClient", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/SPIFFS/src", "-I/home/<USER>/Documents/Arduino/libraries/ArduinoJson/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/DNSServer/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/AsyncUDP/src", "-I/home/<USER>/Documents/Arduino/libraries/Adafruit_GFX_Library", "-I/home/<USER>/Documents/Arduino/libraries/Adafruit_BusIO", "-I/home/<USER>/Documents/Arduino/libraries/ESP32_HUB75_LED_MATRIX_PANEL_DMA_Display/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/HTTPClient/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/NetworkClientSecure/src", "-I/home/<USER>/Documents/Arduino/libraries/UnixTime/src", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/build_opt.h", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/file_opts", "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WiFi/src/AP.cpp", "-o", "/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/libraries/WiFi/AP.cpp.o"], "file": "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WiFi/src/AP.cpp"}, {"directory": "/home/<USER>", "arguments": ["/home/<USER>/.arduino15/packages/esp32/tools/esp-x32/2411/bin/xtensa-esp32s3-elf-g++", "-M<PERSON>", "-c", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/cpp_flags", "-w", "-<PERSON><PERSON>", "-Werror=return-type", "-DF_CPU=240000000L", "-DARDUINO=10607", "-DARDUINO_ESP32S3_DEV", "-DARDUINO_ARCH_ESP32", "-DARDUINO_BOARD=\"ESP32S3_DEV\"", "-DARDUINO_VARIANT=\"esp32s3\"", "-DARDUINO_PARTITION_default", "-DARDUINO_HOST_OS=\"linux\"", "-DARDUINO_FQBN=\"esp32:esp32:esp32s3:UploadSpeed=921600,USBMode=hwcdc,CDCOnBoot=default,MSCOnBoot=default,DFUOnBoot=default,UploadMode=default,CPUFreq=240,FlashMode=qio,FlashSize=4M,PartitionScheme=default,DebugLevel=none,PSRAM=disabled,LoopCore=1,EventsCore=1,EraseFlash=none,JTAGAdapter=default,ZigbeeMode=default\"", "-DESP32=ESP32", "-DCORE_DEBUG_LEVEL=0", "-DARDUINO_RUNNING_CORE=1", "-DARDUINO_EVENT_RUNNING_CORE=1", "-DARDUINO_USB_MODE=1", "-DARDUINO_USB_CDC_ON_BOOT=0", "-DARDUINO_USB_MSC_ON_BOOT=0", "-DARDUINO_USB_DFU_ON_BOOT=0", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/defines", "-I/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta", "-iprefix", "/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/includes", "-I/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/qio_qspi/include", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/variants/esp32s3", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Wire/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/SPI/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WiFi/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Network/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WebServer/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/FS/src", "-I/home/<USER>/Documents/Arduino/libraries/NTPClient", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/SPIFFS/src", "-I/home/<USER>/Documents/Arduino/libraries/ArduinoJson/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/DNSServer/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/AsyncUDP/src", "-I/home/<USER>/Documents/Arduino/libraries/Adafruit_GFX_Library", "-I/home/<USER>/Documents/Arduino/libraries/Adafruit_BusIO", "-I/home/<USER>/Documents/Arduino/libraries/ESP32_HUB75_LED_MATRIX_PANEL_DMA_Display/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/HTTPClient/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/NetworkClientSecure/src", "-I/home/<USER>/Documents/Arduino/libraries/UnixTime/src", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/build_opt.h", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/file_opts", "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WiFi/src/WiFiMulti.cpp", "-o", "/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/libraries/WiFi/WiFiMulti.cpp.o"], "file": "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WiFi/src/WiFiMulti.cpp"}, {"directory": "/home/<USER>", "arguments": ["/home/<USER>/.arduino15/packages/esp32/tools/esp-x32/2411/bin/xtensa-esp32s3-elf-g++", "-M<PERSON>", "-c", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/cpp_flags", "-w", "-<PERSON><PERSON>", "-Werror=return-type", "-DF_CPU=240000000L", "-DARDUINO=10607", "-DARDUINO_ESP32S3_DEV", "-DARDUINO_ARCH_ESP32", "-DARDUINO_BOARD=\"ESP32S3_DEV\"", "-DARDUINO_VARIANT=\"esp32s3\"", "-DARDUINO_PARTITION_default", "-DARDUINO_HOST_OS=\"linux\"", "-DARDUINO_FQBN=\"esp32:esp32:esp32s3:UploadSpeed=921600,USBMode=hwcdc,CDCOnBoot=default,MSCOnBoot=default,DFUOnBoot=default,UploadMode=default,CPUFreq=240,FlashMode=qio,FlashSize=4M,PartitionScheme=default,DebugLevel=none,PSRAM=disabled,LoopCore=1,EventsCore=1,EraseFlash=none,JTAGAdapter=default,ZigbeeMode=default\"", "-DESP32=ESP32", "-DCORE_DEBUG_LEVEL=0", "-DARDUINO_RUNNING_CORE=1", "-DARDUINO_EVENT_RUNNING_CORE=1", "-DARDUINO_USB_MODE=1", "-DARDUINO_USB_CDC_ON_BOOT=0", "-DARDUINO_USB_MSC_ON_BOOT=0", "-DARDUINO_USB_DFU_ON_BOOT=0", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/defines", "-I/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta", "-iprefix", "/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/includes", "-I/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/qio_qspi/include", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/variants/esp32s3", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Wire/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/SPI/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WiFi/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Network/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WebServer/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/FS/src", "-I/home/<USER>/Documents/Arduino/libraries/NTPClient", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/SPIFFS/src", "-I/home/<USER>/Documents/Arduino/libraries/ArduinoJson/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/DNSServer/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/AsyncUDP/src", "-I/home/<USER>/Documents/Arduino/libraries/Adafruit_GFX_Library", "-I/home/<USER>/Documents/Arduino/libraries/Adafruit_BusIO", "-I/home/<USER>/Documents/Arduino/libraries/ESP32_HUB75_LED_MATRIX_PANEL_DMA_Display/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/HTTPClient/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/NetworkClientSecure/src", "-I/home/<USER>/Documents/Arduino/libraries/UnixTime/src", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/build_opt.h", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/file_opts", "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WiFi/src/WiFiSTA.cpp", "-o", "/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/libraries/WiFi/WiFiSTA.cpp.o"], "file": "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WiFi/src/WiFiSTA.cpp"}, {"directory": "/home/<USER>", "arguments": ["/home/<USER>/.arduino15/packages/esp32/tools/esp-x32/2411/bin/xtensa-esp32s3-elf-g++", "-M<PERSON>", "-c", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/cpp_flags", "-w", "-<PERSON><PERSON>", "-Werror=return-type", "-DF_CPU=240000000L", "-DARDUINO=10607", "-DARDUINO_ESP32S3_DEV", "-DARDUINO_ARCH_ESP32", "-DARDUINO_BOARD=\"ESP32S3_DEV\"", "-DARDUINO_VARIANT=\"esp32s3\"", "-DARDUINO_PARTITION_default", "-DARDUINO_HOST_OS=\"linux\"", "-DARDUINO_FQBN=\"esp32:esp32:esp32s3:UploadSpeed=921600,USBMode=hwcdc,CDCOnBoot=default,MSCOnBoot=default,DFUOnBoot=default,UploadMode=default,CPUFreq=240,FlashMode=qio,FlashSize=4M,PartitionScheme=default,DebugLevel=none,PSRAM=disabled,LoopCore=1,EventsCore=1,EraseFlash=none,JTAGAdapter=default,ZigbeeMode=default\"", "-DESP32=ESP32", "-DCORE_DEBUG_LEVEL=0", "-DARDUINO_RUNNING_CORE=1", "-DARDUINO_EVENT_RUNNING_CORE=1", "-DARDUINO_USB_MODE=1", "-DARDUINO_USB_CDC_ON_BOOT=0", "-DARDUINO_USB_MSC_ON_BOOT=0", "-DARDUINO_USB_DFU_ON_BOOT=0", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/defines", "-I/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta", "-iprefix", "/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/includes", "-I/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/qio_qspi/include", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/variants/esp32s3", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Wire/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/SPI/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WiFi/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Network/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WebServer/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/FS/src", "-I/home/<USER>/Documents/Arduino/libraries/NTPClient", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/SPIFFS/src", "-I/home/<USER>/Documents/Arduino/libraries/ArduinoJson/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/DNSServer/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/AsyncUDP/src", "-I/home/<USER>/Documents/Arduino/libraries/Adafruit_GFX_Library", "-I/home/<USER>/Documents/Arduino/libraries/Adafruit_BusIO", "-I/home/<USER>/Documents/Arduino/libraries/ESP32_HUB75_LED_MATRIX_PANEL_DMA_Display/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/HTTPClient/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/NetworkClientSecure/src", "-I/home/<USER>/Documents/Arduino/libraries/UnixTime/src", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/build_opt.h", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/file_opts", "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WiFi/src/WiFi.cpp", "-o", "/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/libraries/WiFi/WiFi.cpp.o"], "file": "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WiFi/src/WiFi.cpp"}, {"directory": "/home/<USER>", "arguments": ["/home/<USER>/.arduino15/packages/esp32/tools/esp-x32/2411/bin/xtensa-esp32s3-elf-g++", "-M<PERSON>", "-c", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/cpp_flags", "-w", "-<PERSON><PERSON>", "-Werror=return-type", "-DF_CPU=240000000L", "-DARDUINO=10607", "-DARDUINO_ESP32S3_DEV", "-DARDUINO_ARCH_ESP32", "-DARDUINO_BOARD=\"ESP32S3_DEV\"", "-DARDUINO_VARIANT=\"esp32s3\"", "-DARDUINO_PARTITION_default", "-DARDUINO_HOST_OS=\"linux\"", "-DARDUINO_FQBN=\"esp32:esp32:esp32s3:UploadSpeed=921600,USBMode=hwcdc,CDCOnBoot=default,MSCOnBoot=default,DFUOnBoot=default,UploadMode=default,CPUFreq=240,FlashMode=qio,FlashSize=4M,PartitionScheme=default,DebugLevel=none,PSRAM=disabled,LoopCore=1,EventsCore=1,EraseFlash=none,JTAGAdapter=default,ZigbeeMode=default\"", "-DESP32=ESP32", "-DCORE_DEBUG_LEVEL=0", "-DARDUINO_RUNNING_CORE=1", "-DARDUINO_EVENT_RUNNING_CORE=1", "-DARDUINO_USB_MODE=1", "-DARDUINO_USB_CDC_ON_BOOT=0", "-DARDUINO_USB_MSC_ON_BOOT=0", "-DARDUINO_USB_DFU_ON_BOOT=0", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/defines", "-I/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta", "-iprefix", "/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/includes", "-I/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/qio_qspi/include", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/variants/esp32s3", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Wire/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/SPI/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WiFi/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Network/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WebServer/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/FS/src", "-I/home/<USER>/Documents/Arduino/libraries/NTPClient", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/SPIFFS/src", "-I/home/<USER>/Documents/Arduino/libraries/ArduinoJson/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/DNSServer/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/AsyncUDP/src", "-I/home/<USER>/Documents/Arduino/libraries/Adafruit_GFX_Library", "-I/home/<USER>/Documents/Arduino/libraries/Adafruit_BusIO", "-I/home/<USER>/Documents/Arduino/libraries/ESP32_HUB75_LED_MATRIX_PANEL_DMA_Display/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/HTTPClient/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/NetworkClientSecure/src", "-I/home/<USER>/Documents/Arduino/libraries/UnixTime/src", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/build_opt.h", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/file_opts", "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WiFi/src/WiFiGeneric.cpp", "-o", "/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/libraries/WiFi/WiFiGeneric.cpp.o"], "file": "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WiFi/src/WiFiGeneric.cpp"}, {"directory": "/home/<USER>", "arguments": ["/home/<USER>/.arduino15/packages/esp32/tools/esp-x32/2411/bin/xtensa-esp32s3-elf-g++", "-M<PERSON>", "-c", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/cpp_flags", "-w", "-<PERSON><PERSON>", "-Werror=return-type", "-DF_CPU=240000000L", "-DARDUINO=10607", "-DARDUINO_ESP32S3_DEV", "-DARDUINO_ARCH_ESP32", "-DARDUINO_BOARD=\"ESP32S3_DEV\"", "-DARDUINO_VARIANT=\"esp32s3\"", "-DARDUINO_PARTITION_default", "-DARDUINO_HOST_OS=\"linux\"", "-DARDUINO_FQBN=\"esp32:esp32:esp32s3:UploadSpeed=921600,USBMode=hwcdc,CDCOnBoot=default,MSCOnBoot=default,DFUOnBoot=default,UploadMode=default,CPUFreq=240,FlashMode=qio,FlashSize=4M,PartitionScheme=default,DebugLevel=none,PSRAM=disabled,LoopCore=1,EventsCore=1,EraseFlash=none,JTAGAdapter=default,ZigbeeMode=default\"", "-DESP32=ESP32", "-DCORE_DEBUG_LEVEL=0", "-DARDUINO_RUNNING_CORE=1", "-DARDUINO_EVENT_RUNNING_CORE=1", "-DARDUINO_USB_MODE=1", "-DARDUINO_USB_CDC_ON_BOOT=0", "-DARDUINO_USB_MSC_ON_BOOT=0", "-DARDUINO_USB_DFU_ON_BOOT=0", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/defines", "-I/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta", "-iprefix", "/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/includes", "-I/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/qio_qspi/include", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/variants/esp32s3", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Wire/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/SPI/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WiFi/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Network/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WebServer/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/FS/src", "-I/home/<USER>/Documents/Arduino/libraries/NTPClient", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/SPIFFS/src", "-I/home/<USER>/Documents/Arduino/libraries/ArduinoJson/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/DNSServer/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/AsyncUDP/src", "-I/home/<USER>/Documents/Arduino/libraries/Adafruit_GFX_Library", "-I/home/<USER>/Documents/Arduino/libraries/Adafruit_BusIO", "-I/home/<USER>/Documents/Arduino/libraries/ESP32_HUB75_LED_MATRIX_PANEL_DMA_Display/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/HTTPClient/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/NetworkClientSecure/src", "-I/home/<USER>/Documents/Arduino/libraries/UnixTime/src", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/build_opt.h", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/file_opts", "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WiFi/src/STA.cpp", "-o", "/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/libraries/WiFi/STA.cpp.o"], "file": "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WiFi/src/STA.cpp"}, {"directory": "/home/<USER>", "arguments": ["/home/<USER>/.arduino15/packages/esp32/tools/esp-x32/2411/bin/xtensa-esp32s3-elf-g++", "-M<PERSON>", "-c", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/cpp_flags", "-w", "-<PERSON><PERSON>", "-Werror=return-type", "-DF_CPU=240000000L", "-DARDUINO=10607", "-DARDUINO_ESP32S3_DEV", "-DARDUINO_ARCH_ESP32", "-DARDUINO_BOARD=\"ESP32S3_DEV\"", "-DARDUINO_VARIANT=\"esp32s3\"", "-DARDUINO_PARTITION_default", "-DARDUINO_HOST_OS=\"linux\"", "-DARDUINO_FQBN=\"esp32:esp32:esp32s3:UploadSpeed=921600,USBMode=hwcdc,CDCOnBoot=default,MSCOnBoot=default,DFUOnBoot=default,UploadMode=default,CPUFreq=240,FlashMode=qio,FlashSize=4M,PartitionScheme=default,DebugLevel=none,PSRAM=disabled,LoopCore=1,EventsCore=1,EraseFlash=none,JTAGAdapter=default,ZigbeeMode=default\"", "-DESP32=ESP32", "-DCORE_DEBUG_LEVEL=0", "-DARDUINO_RUNNING_CORE=1", "-DARDUINO_EVENT_RUNNING_CORE=1", "-DARDUINO_USB_MODE=1", "-DARDUINO_USB_CDC_ON_BOOT=0", "-DARDUINO_USB_MSC_ON_BOOT=0", "-DARDUINO_USB_DFU_ON_BOOT=0", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/defines", "-I/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta", "-iprefix", "/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/includes", "-I/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/qio_qspi/include", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/variants/esp32s3", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Wire/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/SPI/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WiFi/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Network/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WebServer/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/FS/src", "-I/home/<USER>/Documents/Arduino/libraries/NTPClient", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/SPIFFS/src", "-I/home/<USER>/Documents/Arduino/libraries/ArduinoJson/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/DNSServer/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/AsyncUDP/src", "-I/home/<USER>/Documents/Arduino/libraries/Adafruit_GFX_Library", "-I/home/<USER>/Documents/Arduino/libraries/Adafruit_BusIO", "-I/home/<USER>/Documents/Arduino/libraries/ESP32_HUB75_LED_MATRIX_PANEL_DMA_Display/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/HTTPClient/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/NetworkClientSecure/src", "-I/home/<USER>/Documents/Arduino/libraries/UnixTime/src", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/build_opt.h", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/file_opts", "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Network/src/NetworkServer.cpp", "-o", "/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/libraries/Network/NetworkServer.cpp.o"], "file": "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Network/src/NetworkServer.cpp"}, {"directory": "/home/<USER>", "arguments": ["/home/<USER>/.arduino15/packages/esp32/tools/esp-x32/2411/bin/xtensa-esp32s3-elf-g++", "-M<PERSON>", "-c", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/cpp_flags", "-w", "-<PERSON><PERSON>", "-Werror=return-type", "-DF_CPU=240000000L", "-DARDUINO=10607", "-DARDUINO_ESP32S3_DEV", "-DARDUINO_ARCH_ESP32", "-DARDUINO_BOARD=\"ESP32S3_DEV\"", "-DARDUINO_VARIANT=\"esp32s3\"", "-DARDUINO_PARTITION_default", "-DARDUINO_HOST_OS=\"linux\"", "-DARDUINO_FQBN=\"esp32:esp32:esp32s3:UploadSpeed=921600,USBMode=hwcdc,CDCOnBoot=default,MSCOnBoot=default,DFUOnBoot=default,UploadMode=default,CPUFreq=240,FlashMode=qio,FlashSize=4M,PartitionScheme=default,DebugLevel=none,PSRAM=disabled,LoopCore=1,EventsCore=1,EraseFlash=none,JTAGAdapter=default,ZigbeeMode=default\"", "-DESP32=ESP32", "-DCORE_DEBUG_LEVEL=0", "-DARDUINO_RUNNING_CORE=1", "-DARDUINO_EVENT_RUNNING_CORE=1", "-DARDUINO_USB_MODE=1", "-DARDUINO_USB_CDC_ON_BOOT=0", "-DARDUINO_USB_MSC_ON_BOOT=0", "-DARDUINO_USB_DFU_ON_BOOT=0", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/defines", "-I/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta", "-iprefix", "/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/includes", "-I/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/qio_qspi/include", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/variants/esp32s3", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Wire/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/SPI/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WiFi/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Network/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WebServer/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/FS/src", "-I/home/<USER>/Documents/Arduino/libraries/NTPClient", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/SPIFFS/src", "-I/home/<USER>/Documents/Arduino/libraries/ArduinoJson/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/DNSServer/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/AsyncUDP/src", "-I/home/<USER>/Documents/Arduino/libraries/Adafruit_GFX_Library", "-I/home/<USER>/Documents/Arduino/libraries/Adafruit_BusIO", "-I/home/<USER>/Documents/Arduino/libraries/ESP32_HUB75_LED_MATRIX_PANEL_DMA_Display/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/HTTPClient/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/NetworkClientSecure/src", "-I/home/<USER>/Documents/Arduino/libraries/UnixTime/src", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/build_opt.h", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/file_opts", "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Network/src/NetworkUdp.cpp", "-o", "/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/libraries/Network/NetworkUdp.cpp.o"], "file": "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Network/src/NetworkUdp.cpp"}, {"directory": "/home/<USER>", "arguments": ["/home/<USER>/.arduino15/packages/esp32/tools/esp-x32/2411/bin/xtensa-esp32s3-elf-g++", "-M<PERSON>", "-c", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/cpp_flags", "-w", "-<PERSON><PERSON>", "-Werror=return-type", "-DF_CPU=240000000L", "-DARDUINO=10607", "-DARDUINO_ESP32S3_DEV", "-DARDUINO_ARCH_ESP32", "-DARDUINO_BOARD=\"ESP32S3_DEV\"", "-DARDUINO_VARIANT=\"esp32s3\"", "-DARDUINO_PARTITION_default", "-DARDUINO_HOST_OS=\"linux\"", "-DARDUINO_FQBN=\"esp32:esp32:esp32s3:UploadSpeed=921600,USBMode=hwcdc,CDCOnBoot=default,MSCOnBoot=default,DFUOnBoot=default,UploadMode=default,CPUFreq=240,FlashMode=qio,FlashSize=4M,PartitionScheme=default,DebugLevel=none,PSRAM=disabled,LoopCore=1,EventsCore=1,EraseFlash=none,JTAGAdapter=default,ZigbeeMode=default\"", "-DESP32=ESP32", "-DCORE_DEBUG_LEVEL=0", "-DARDUINO_RUNNING_CORE=1", "-DARDUINO_EVENT_RUNNING_CORE=1", "-DARDUINO_USB_MODE=1", "-DARDUINO_USB_CDC_ON_BOOT=0", "-DARDUINO_USB_MSC_ON_BOOT=0", "-DARDUINO_USB_DFU_ON_BOOT=0", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/defines", "-I/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta", "-iprefix", "/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/includes", "-I/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/qio_qspi/include", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/variants/esp32s3", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Wire/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/SPI/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WiFi/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Network/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WebServer/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/FS/src", "-I/home/<USER>/Documents/Arduino/libraries/NTPClient", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/SPIFFS/src", "-I/home/<USER>/Documents/Arduino/libraries/ArduinoJson/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/DNSServer/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/AsyncUDP/src", "-I/home/<USER>/Documents/Arduino/libraries/Adafruit_GFX_Library", "-I/home/<USER>/Documents/Arduino/libraries/Adafruit_BusIO", "-I/home/<USER>/Documents/Arduino/libraries/ESP32_HUB75_LED_MATRIX_PANEL_DMA_Display/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/HTTPClient/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/NetworkClientSecure/src", "-I/home/<USER>/Documents/Arduino/libraries/UnixTime/src", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/build_opt.h", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/file_opts", "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Network/src/NetworkClient.cpp", "-o", "/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/libraries/Network/NetworkClient.cpp.o"], "file": "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Network/src/NetworkClient.cpp"}, {"directory": "/home/<USER>", "arguments": ["/home/<USER>/.arduino15/packages/esp32/tools/esp-x32/2411/bin/xtensa-esp32s3-elf-g++", "-M<PERSON>", "-c", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/cpp_flags", "-w", "-<PERSON><PERSON>", "-Werror=return-type", "-DF_CPU=240000000L", "-DARDUINO=10607", "-DARDUINO_ESP32S3_DEV", "-DARDUINO_ARCH_ESP32", "-DARDUINO_BOARD=\"ESP32S3_DEV\"", "-DARDUINO_VARIANT=\"esp32s3\"", "-DARDUINO_PARTITION_default", "-DARDUINO_HOST_OS=\"linux\"", "-DARDUINO_FQBN=\"esp32:esp32:esp32s3:UploadSpeed=921600,USBMode=hwcdc,CDCOnBoot=default,MSCOnBoot=default,DFUOnBoot=default,UploadMode=default,CPUFreq=240,FlashMode=qio,FlashSize=4M,PartitionScheme=default,DebugLevel=none,PSRAM=disabled,LoopCore=1,EventsCore=1,EraseFlash=none,JTAGAdapter=default,ZigbeeMode=default\"", "-DESP32=ESP32", "-DCORE_DEBUG_LEVEL=0", "-DARDUINO_RUNNING_CORE=1", "-DARDUINO_EVENT_RUNNING_CORE=1", "-DARDUINO_USB_MODE=1", "-DARDUINO_USB_CDC_ON_BOOT=0", "-DARDUINO_USB_MSC_ON_BOOT=0", "-DARDUINO_USB_DFU_ON_BOOT=0", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/defines", "-I/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta", "-iprefix", "/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/includes", "-I/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/qio_qspi/include", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/variants/esp32s3", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Wire/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/SPI/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WiFi/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Network/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WebServer/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/FS/src", "-I/home/<USER>/Documents/Arduino/libraries/NTPClient", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/SPIFFS/src", "-I/home/<USER>/Documents/Arduino/libraries/ArduinoJson/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/DNSServer/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/AsyncUDP/src", "-I/home/<USER>/Documents/Arduino/libraries/Adafruit_GFX_Library", "-I/home/<USER>/Documents/Arduino/libraries/Adafruit_BusIO", "-I/home/<USER>/Documents/Arduino/libraries/ESP32_HUB75_LED_MATRIX_PANEL_DMA_Display/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/HTTPClient/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/NetworkClientSecure/src", "-I/home/<USER>/Documents/Arduino/libraries/UnixTime/src", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/build_opt.h", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/file_opts", "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Network/src/NetworkManager.cpp", "-o", "/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/libraries/Network/NetworkManager.cpp.o"], "file": "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Network/src/NetworkManager.cpp"}, {"directory": "/home/<USER>", "arguments": ["/home/<USER>/.arduino15/packages/esp32/tools/esp-x32/2411/bin/xtensa-esp32s3-elf-g++", "-M<PERSON>", "-c", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/cpp_flags", "-w", "-<PERSON><PERSON>", "-Werror=return-type", "-DF_CPU=240000000L", "-DARDUINO=10607", "-DARDUINO_ESP32S3_DEV", "-DARDUINO_ARCH_ESP32", "-DARDUINO_BOARD=\"ESP32S3_DEV\"", "-DARDUINO_VARIANT=\"esp32s3\"", "-DARDUINO_PARTITION_default", "-DARDUINO_HOST_OS=\"linux\"", "-DARDUINO_FQBN=\"esp32:esp32:esp32s3:UploadSpeed=921600,USBMode=hwcdc,CDCOnBoot=default,MSCOnBoot=default,DFUOnBoot=default,UploadMode=default,CPUFreq=240,FlashMode=qio,FlashSize=4M,PartitionScheme=default,DebugLevel=none,PSRAM=disabled,LoopCore=1,EventsCore=1,EraseFlash=none,JTAGAdapter=default,ZigbeeMode=default\"", "-DESP32=ESP32", "-DCORE_DEBUG_LEVEL=0", "-DARDUINO_RUNNING_CORE=1", "-DARDUINO_EVENT_RUNNING_CORE=1", "-DARDUINO_USB_MODE=1", "-DARDUINO_USB_CDC_ON_BOOT=0", "-DARDUINO_USB_MSC_ON_BOOT=0", "-DARDUINO_USB_DFU_ON_BOOT=0", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/defines", "-I/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta", "-iprefix", "/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/includes", "-I/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/qio_qspi/include", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/variants/esp32s3", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Wire/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/SPI/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WiFi/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Network/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WebServer/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/FS/src", "-I/home/<USER>/Documents/Arduino/libraries/NTPClient", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/SPIFFS/src", "-I/home/<USER>/Documents/Arduino/libraries/ArduinoJson/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/DNSServer/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/AsyncUDP/src", "-I/home/<USER>/Documents/Arduino/libraries/Adafruit_GFX_Library", "-I/home/<USER>/Documents/Arduino/libraries/Adafruit_BusIO", "-I/home/<USER>/Documents/Arduino/libraries/ESP32_HUB75_LED_MATRIX_PANEL_DMA_Display/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/HTTPClient/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/NetworkClientSecure/src", "-I/home/<USER>/Documents/Arduino/libraries/UnixTime/src", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/build_opt.h", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/file_opts", "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Network/src/NetworkEvents.cpp", "-o", "/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/libraries/Network/NetworkEvents.cpp.o"], "file": "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Network/src/NetworkEvents.cpp"}, {"directory": "/home/<USER>", "arguments": ["/home/<USER>/.arduino15/packages/esp32/tools/esp-x32/2411/bin/xtensa-esp32s3-elf-g++", "-M<PERSON>", "-c", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/cpp_flags", "-w", "-<PERSON><PERSON>", "-Werror=return-type", "-DF_CPU=240000000L", "-DARDUINO=10607", "-DARDUINO_ESP32S3_DEV", "-DARDUINO_ARCH_ESP32", "-DARDUINO_BOARD=\"ESP32S3_DEV\"", "-DARDUINO_VARIANT=\"esp32s3\"", "-DARDUINO_PARTITION_default", "-DARDUINO_HOST_OS=\"linux\"", "-DARDUINO_FQBN=\"esp32:esp32:esp32s3:UploadSpeed=921600,USBMode=hwcdc,CDCOnBoot=default,MSCOnBoot=default,DFUOnBoot=default,UploadMode=default,CPUFreq=240,FlashMode=qio,FlashSize=4M,PartitionScheme=default,DebugLevel=none,PSRAM=disabled,LoopCore=1,EventsCore=1,EraseFlash=none,JTAGAdapter=default,ZigbeeMode=default\"", "-DESP32=ESP32", "-DCORE_DEBUG_LEVEL=0", "-DARDUINO_RUNNING_CORE=1", "-DARDUINO_EVENT_RUNNING_CORE=1", "-DARDUINO_USB_MODE=1", "-DARDUINO_USB_CDC_ON_BOOT=0", "-DARDUINO_USB_MSC_ON_BOOT=0", "-DARDUINO_USB_DFU_ON_BOOT=0", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/defines", "-I/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta", "-iprefix", "/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/includes", "-I/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/qio_qspi/include", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/variants/esp32s3", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Wire/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/SPI/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WiFi/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Network/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WebServer/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/FS/src", "-I/home/<USER>/Documents/Arduino/libraries/NTPClient", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/SPIFFS/src", "-I/home/<USER>/Documents/Arduino/libraries/ArduinoJson/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/DNSServer/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/AsyncUDP/src", "-I/home/<USER>/Documents/Arduino/libraries/Adafruit_GFX_Library", "-I/home/<USER>/Documents/Arduino/libraries/Adafruit_BusIO", "-I/home/<USER>/Documents/Arduino/libraries/ESP32_HUB75_LED_MATRIX_PANEL_DMA_Display/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/HTTPClient/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/NetworkClientSecure/src", "-I/home/<USER>/Documents/Arduino/libraries/UnixTime/src", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/build_opt.h", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/file_opts", "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Network/src/NetworkInterface.cpp", "-o", "/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/libraries/Network/NetworkInterface.cpp.o"], "file": "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Network/src/NetworkInterface.cpp"}, {"directory": "/home/<USER>", "arguments": ["/home/<USER>/.arduino15/packages/esp32/tools/esp-x32/2411/bin/xtensa-esp32s3-elf-g++", "-M<PERSON>", "-c", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/cpp_flags", "-w", "-<PERSON><PERSON>", "-Werror=return-type", "-DF_CPU=240000000L", "-DARDUINO=10607", "-DARDUINO_ESP32S3_DEV", "-DARDUINO_ARCH_ESP32", "-DARDUINO_BOARD=\"ESP32S3_DEV\"", "-DARDUINO_VARIANT=\"esp32s3\"", "-DARDUINO_PARTITION_default", "-DARDUINO_HOST_OS=\"linux\"", "-DARDUINO_FQBN=\"esp32:esp32:esp32s3:UploadSpeed=921600,USBMode=hwcdc,CDCOnBoot=default,MSCOnBoot=default,DFUOnBoot=default,UploadMode=default,CPUFreq=240,FlashMode=qio,FlashSize=4M,PartitionScheme=default,DebugLevel=none,PSRAM=disabled,LoopCore=1,EventsCore=1,EraseFlash=none,JTAGAdapter=default,ZigbeeMode=default\"", "-DESP32=ESP32", "-DCORE_DEBUG_LEVEL=0", "-DARDUINO_RUNNING_CORE=1", "-DARDUINO_EVENT_RUNNING_CORE=1", "-DARDUINO_USB_MODE=1", "-DARDUINO_USB_CDC_ON_BOOT=0", "-DARDUINO_USB_MSC_ON_BOOT=0", "-DARDUINO_USB_DFU_ON_BOOT=0", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/defines", "-I/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta", "-iprefix", "/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/includes", "-I/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/qio_qspi/include", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/variants/esp32s3", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Wire/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/SPI/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WiFi/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Network/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WebServer/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/FS/src", "-I/home/<USER>/Documents/Arduino/libraries/NTPClient", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/SPIFFS/src", "-I/home/<USER>/Documents/Arduino/libraries/ArduinoJson/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/DNSServer/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/AsyncUDP/src", "-I/home/<USER>/Documents/Arduino/libraries/Adafruit_GFX_Library", "-I/home/<USER>/Documents/Arduino/libraries/Adafruit_BusIO", "-I/home/<USER>/Documents/Arduino/libraries/ESP32_HUB75_LED_MATRIX_PANEL_DMA_Display/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/HTTPClient/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/NetworkClientSecure/src", "-I/home/<USER>/Documents/Arduino/libraries/UnixTime/src", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/build_opt.h", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/file_opts", "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WebServer/src/WebServer.cpp", "-o", "/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/libraries/WebServer/WebServer.cpp.o"], "file": "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WebServer/src/WebServer.cpp"}, {"directory": "/home/<USER>", "arguments": ["/home/<USER>/.arduino15/packages/esp32/tools/esp-x32/2411/bin/xtensa-esp32s3-elf-g++", "-M<PERSON>", "-c", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/cpp_flags", "-w", "-<PERSON><PERSON>", "-Werror=return-type", "-DF_CPU=240000000L", "-DARDUINO=10607", "-DARDUINO_ESP32S3_DEV", "-DARDUINO_ARCH_ESP32", "-DARDUINO_BOARD=\"ESP32S3_DEV\"", "-DARDUINO_VARIANT=\"esp32s3\"", "-DARDUINO_PARTITION_default", "-DARDUINO_HOST_OS=\"linux\"", "-DARDUINO_FQBN=\"esp32:esp32:esp32s3:UploadSpeed=921600,USBMode=hwcdc,CDCOnBoot=default,MSCOnBoot=default,DFUOnBoot=default,UploadMode=default,CPUFreq=240,FlashMode=qio,FlashSize=4M,PartitionScheme=default,DebugLevel=none,PSRAM=disabled,LoopCore=1,EventsCore=1,EraseFlash=none,JTAGAdapter=default,ZigbeeMode=default\"", "-DESP32=ESP32", "-DCORE_DEBUG_LEVEL=0", "-DARDUINO_RUNNING_CORE=1", "-DARDUINO_EVENT_RUNNING_CORE=1", "-DARDUINO_USB_MODE=1", "-DARDUINO_USB_CDC_ON_BOOT=0", "-DARDUINO_USB_MSC_ON_BOOT=0", "-DARDUINO_USB_DFU_ON_BOOT=0", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/defines", "-I/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta", "-iprefix", "/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/includes", "-I/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/qio_qspi/include", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/variants/esp32s3", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Wire/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/SPI/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WiFi/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Network/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WebServer/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/FS/src", "-I/home/<USER>/Documents/Arduino/libraries/NTPClient", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/SPIFFS/src", "-I/home/<USER>/Documents/Arduino/libraries/ArduinoJson/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/DNSServer/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/AsyncUDP/src", "-I/home/<USER>/Documents/Arduino/libraries/Adafruit_GFX_Library", "-I/home/<USER>/Documents/Arduino/libraries/Adafruit_BusIO", "-I/home/<USER>/Documents/Arduino/libraries/ESP32_HUB75_LED_MATRIX_PANEL_DMA_Display/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/HTTPClient/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/NetworkClientSecure/src", "-I/home/<USER>/Documents/Arduino/libraries/UnixTime/src", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/build_opt.h", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/file_opts", "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WebServer/src/middleware/LoggingMiddleware.cpp", "-o", "/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/libraries/WebServer/middleware/LoggingMiddleware.cpp.o"], "file": "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WebServer/src/middleware/LoggingMiddleware.cpp"}, {"directory": "/home/<USER>", "arguments": ["/home/<USER>/.arduino15/packages/esp32/tools/esp-x32/2411/bin/xtensa-esp32s3-elf-g++", "-M<PERSON>", "-c", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/cpp_flags", "-w", "-<PERSON><PERSON>", "-Werror=return-type", "-DF_CPU=240000000L", "-DARDUINO=10607", "-DARDUINO_ESP32S3_DEV", "-DARDUINO_ARCH_ESP32", "-DARDUINO_BOARD=\"ESP32S3_DEV\"", "-DARDUINO_VARIANT=\"esp32s3\"", "-DARDUINO_PARTITION_default", "-DARDUINO_HOST_OS=\"linux\"", "-DARDUINO_FQBN=\"esp32:esp32:esp32s3:UploadSpeed=921600,USBMode=hwcdc,CDCOnBoot=default,MSCOnBoot=default,DFUOnBoot=default,UploadMode=default,CPUFreq=240,FlashMode=qio,FlashSize=4M,PartitionScheme=default,DebugLevel=none,PSRAM=disabled,LoopCore=1,EventsCore=1,EraseFlash=none,JTAGAdapter=default,ZigbeeMode=default\"", "-DESP32=ESP32", "-DCORE_DEBUG_LEVEL=0", "-DARDUINO_RUNNING_CORE=1", "-DARDUINO_EVENT_RUNNING_CORE=1", "-DARDUINO_USB_MODE=1", "-DARDUINO_USB_CDC_ON_BOOT=0", "-DARDUINO_USB_MSC_ON_BOOT=0", "-DARDUINO_USB_DFU_ON_BOOT=0", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/defines", "-I/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta", "-iprefix", "/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/includes", "-I/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/qio_qspi/include", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/variants/esp32s3", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Wire/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/SPI/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WiFi/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Network/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WebServer/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/FS/src", "-I/home/<USER>/Documents/Arduino/libraries/NTPClient", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/SPIFFS/src", "-I/home/<USER>/Documents/Arduino/libraries/ArduinoJson/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/DNSServer/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/AsyncUDP/src", "-I/home/<USER>/Documents/Arduino/libraries/Adafruit_GFX_Library", "-I/home/<USER>/Documents/Arduino/libraries/Adafruit_BusIO", "-I/home/<USER>/Documents/Arduino/libraries/ESP32_HUB75_LED_MATRIX_PANEL_DMA_Display/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/HTTPClient/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/NetworkClientSecure/src", "-I/home/<USER>/Documents/Arduino/libraries/UnixTime/src", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/build_opt.h", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/file_opts", "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WebServer/src/detail/mimetable.cpp", "-o", "/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/libraries/WebServer/detail/mimetable.cpp.o"], "file": "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WebServer/src/detail/mimetable.cpp"}, {"directory": "/home/<USER>", "arguments": ["/home/<USER>/.arduino15/packages/esp32/tools/esp-x32/2411/bin/xtensa-esp32s3-elf-g++", "-M<PERSON>", "-c", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/cpp_flags", "-w", "-<PERSON><PERSON>", "-Werror=return-type", "-DF_CPU=240000000L", "-DARDUINO=10607", "-DARDUINO_ESP32S3_DEV", "-DARDUINO_ARCH_ESP32", "-DARDUINO_BOARD=\"ESP32S3_DEV\"", "-DARDUINO_VARIANT=\"esp32s3\"", "-DARDUINO_PARTITION_default", "-DARDUINO_HOST_OS=\"linux\"", "-DARDUINO_FQBN=\"esp32:esp32:esp32s3:UploadSpeed=921600,USBMode=hwcdc,CDCOnBoot=default,MSCOnBoot=default,DFUOnBoot=default,UploadMode=default,CPUFreq=240,FlashMode=qio,FlashSize=4M,PartitionScheme=default,DebugLevel=none,PSRAM=disabled,LoopCore=1,EventsCore=1,EraseFlash=none,JTAGAdapter=default,ZigbeeMode=default\"", "-DESP32=ESP32", "-DCORE_DEBUG_LEVEL=0", "-DARDUINO_RUNNING_CORE=1", "-DARDUINO_EVENT_RUNNING_CORE=1", "-DARDUINO_USB_MODE=1", "-DARDUINO_USB_CDC_ON_BOOT=0", "-DARDUINO_USB_MSC_ON_BOOT=0", "-DARDUINO_USB_DFU_ON_BOOT=0", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/defines", "-I/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta", "-iprefix", "/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/includes", "-I/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/qio_qspi/include", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/variants/esp32s3", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Wire/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/SPI/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WiFi/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Network/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WebServer/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/FS/src", "-I/home/<USER>/Documents/Arduino/libraries/NTPClient", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/SPIFFS/src", "-I/home/<USER>/Documents/Arduino/libraries/ArduinoJson/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/DNSServer/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/AsyncUDP/src", "-I/home/<USER>/Documents/Arduino/libraries/Adafruit_GFX_Library", "-I/home/<USER>/Documents/Arduino/libraries/Adafruit_BusIO", "-I/home/<USER>/Documents/Arduino/libraries/ESP32_HUB75_LED_MATRIX_PANEL_DMA_Display/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/HTTPClient/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/NetworkClientSecure/src", "-I/home/<USER>/Documents/Arduino/libraries/UnixTime/src", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/build_opt.h", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/file_opts", "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WebServer/src/middleware/AuthenticationMiddleware.cpp", "-o", "/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/libraries/WebServer/middleware/AuthenticationMiddleware.cpp.o"], "file": "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WebServer/src/middleware/AuthenticationMiddleware.cpp"}, {"directory": "/home/<USER>", "arguments": ["/home/<USER>/.arduino15/packages/esp32/tools/esp-x32/2411/bin/xtensa-esp32s3-elf-g++", "-M<PERSON>", "-c", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/cpp_flags", "-w", "-<PERSON><PERSON>", "-Werror=return-type", "-DF_CPU=240000000L", "-DARDUINO=10607", "-DARDUINO_ESP32S3_DEV", "-DARDUINO_ARCH_ESP32", "-DARDUINO_BOARD=\"ESP32S3_DEV\"", "-DARDUINO_VARIANT=\"esp32s3\"", "-DARDUINO_PARTITION_default", "-DARDUINO_HOST_OS=\"linux\"", "-DARDUINO_FQBN=\"esp32:esp32:esp32s3:UploadSpeed=921600,USBMode=hwcdc,CDCOnBoot=default,MSCOnBoot=default,DFUOnBoot=default,UploadMode=default,CPUFreq=240,FlashMode=qio,FlashSize=4M,PartitionScheme=default,DebugLevel=none,PSRAM=disabled,LoopCore=1,EventsCore=1,EraseFlash=none,JTAGAdapter=default,ZigbeeMode=default\"", "-DESP32=ESP32", "-DCORE_DEBUG_LEVEL=0", "-DARDUINO_RUNNING_CORE=1", "-DARDUINO_EVENT_RUNNING_CORE=1", "-DARDUINO_USB_MODE=1", "-DARDUINO_USB_CDC_ON_BOOT=0", "-DARDUINO_USB_MSC_ON_BOOT=0", "-DARDUINO_USB_DFU_ON_BOOT=0", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/defines", "-I/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta", "-iprefix", "/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/includes", "-I/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/qio_qspi/include", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/variants/esp32s3", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Wire/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/SPI/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WiFi/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Network/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WebServer/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/FS/src", "-I/home/<USER>/Documents/Arduino/libraries/NTPClient", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/SPIFFS/src", "-I/home/<USER>/Documents/Arduino/libraries/ArduinoJson/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/DNSServer/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/AsyncUDP/src", "-I/home/<USER>/Documents/Arduino/libraries/Adafruit_GFX_Library", "-I/home/<USER>/Documents/Arduino/libraries/Adafruit_BusIO", "-I/home/<USER>/Documents/Arduino/libraries/ESP32_HUB75_LED_MATRIX_PANEL_DMA_Display/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/HTTPClient/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/NetworkClientSecure/src", "-I/home/<USER>/Documents/Arduino/libraries/UnixTime/src", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/build_opt.h", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/file_opts", "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WebServer/src/middleware/CorsMiddleware.cpp", "-o", "/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/libraries/WebServer/middleware/CorsMiddleware.cpp.o"], "file": "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WebServer/src/middleware/CorsMiddleware.cpp"}, {"directory": "/home/<USER>", "arguments": ["/home/<USER>/.arduino15/packages/esp32/tools/esp-x32/2411/bin/xtensa-esp32s3-elf-g++", "-M<PERSON>", "-c", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/cpp_flags", "-w", "-<PERSON><PERSON>", "-Werror=return-type", "-DF_CPU=240000000L", "-DARDUINO=10607", "-DARDUINO_ESP32S3_DEV", "-DARDUINO_ARCH_ESP32", "-DARDUINO_BOARD=\"ESP32S3_DEV\"", "-DARDUINO_VARIANT=\"esp32s3\"", "-DARDUINO_PARTITION_default", "-DARDUINO_HOST_OS=\"linux\"", "-DARDUINO_FQBN=\"esp32:esp32:esp32s3:UploadSpeed=921600,USBMode=hwcdc,CDCOnBoot=default,MSCOnBoot=default,DFUOnBoot=default,UploadMode=default,CPUFreq=240,FlashMode=qio,FlashSize=4M,PartitionScheme=default,DebugLevel=none,PSRAM=disabled,LoopCore=1,EventsCore=1,EraseFlash=none,JTAGAdapter=default,ZigbeeMode=default\"", "-DESP32=ESP32", "-DCORE_DEBUG_LEVEL=0", "-DARDUINO_RUNNING_CORE=1", "-DARDUINO_EVENT_RUNNING_CORE=1", "-DARDUINO_USB_MODE=1", "-DARDUINO_USB_CDC_ON_BOOT=0", "-DARDUINO_USB_MSC_ON_BOOT=0", "-DARDUINO_USB_DFU_ON_BOOT=0", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/defines", "-I/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta", "-iprefix", "/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/includes", "-I/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/qio_qspi/include", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/variants/esp32s3", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Wire/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/SPI/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WiFi/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Network/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WebServer/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/FS/src", "-I/home/<USER>/Documents/Arduino/libraries/NTPClient", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/SPIFFS/src", "-I/home/<USER>/Documents/Arduino/libraries/ArduinoJson/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/DNSServer/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/AsyncUDP/src", "-I/home/<USER>/Documents/Arduino/libraries/Adafruit_GFX_Library", "-I/home/<USER>/Documents/Arduino/libraries/Adafruit_BusIO", "-I/home/<USER>/Documents/Arduino/libraries/ESP32_HUB75_LED_MATRIX_PANEL_DMA_Display/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/HTTPClient/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/NetworkClientSecure/src", "-I/home/<USER>/Documents/Arduino/libraries/UnixTime/src", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/build_opt.h", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/file_opts", "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WebServer/src/Parsing.cpp", "-o", "/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/libraries/WebServer/Parsing.cpp.o"], "file": "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WebServer/src/Parsing.cpp"}, {"directory": "/home/<USER>", "arguments": ["/home/<USER>/.arduino15/packages/esp32/tools/esp-x32/2411/bin/xtensa-esp32s3-elf-g++", "-M<PERSON>", "-c", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/cpp_flags", "-w", "-<PERSON><PERSON>", "-Werror=return-type", "-DF_CPU=240000000L", "-DARDUINO=10607", "-DARDUINO_ESP32S3_DEV", "-DARDUINO_ARCH_ESP32", "-DARDUINO_BOARD=\"ESP32S3_DEV\"", "-DARDUINO_VARIANT=\"esp32s3\"", "-DARDUINO_PARTITION_default", "-DARDUINO_HOST_OS=\"linux\"", "-DARDUINO_FQBN=\"esp32:esp32:esp32s3:UploadSpeed=921600,USBMode=hwcdc,CDCOnBoot=default,MSCOnBoot=default,DFUOnBoot=default,UploadMode=default,CPUFreq=240,FlashMode=qio,FlashSize=4M,PartitionScheme=default,DebugLevel=none,PSRAM=disabled,LoopCore=1,EventsCore=1,EraseFlash=none,JTAGAdapter=default,ZigbeeMode=default\"", "-DESP32=ESP32", "-DCORE_DEBUG_LEVEL=0", "-DARDUINO_RUNNING_CORE=1", "-DARDUINO_EVENT_RUNNING_CORE=1", "-DARDUINO_USB_MODE=1", "-DARDUINO_USB_CDC_ON_BOOT=0", "-DARDUINO_USB_MSC_ON_BOOT=0", "-DARDUINO_USB_DFU_ON_BOOT=0", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/defines", "-I/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta", "-iprefix", "/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/includes", "-I/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/qio_qspi/include", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/variants/esp32s3", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Wire/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/SPI/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WiFi/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Network/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WebServer/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/FS/src", "-I/home/<USER>/Documents/Arduino/libraries/NTPClient", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/SPIFFS/src", "-I/home/<USER>/Documents/Arduino/libraries/ArduinoJson/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/DNSServer/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/AsyncUDP/src", "-I/home/<USER>/Documents/Arduino/libraries/Adafruit_GFX_Library", "-I/home/<USER>/Documents/Arduino/libraries/Adafruit_BusIO", "-I/home/<USER>/Documents/Arduino/libraries/ESP32_HUB75_LED_MATRIX_PANEL_DMA_Display/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/HTTPClient/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/NetworkClientSecure/src", "-I/home/<USER>/Documents/Arduino/libraries/UnixTime/src", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/build_opt.h", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/file_opts", "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WebServer/src/middleware/MiddlewareChain.cpp", "-o", "/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/libraries/WebServer/middleware/MiddlewareChain.cpp.o"], "file": "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WebServer/src/middleware/MiddlewareChain.cpp"}, {"directory": "/home/<USER>", "arguments": ["/home/<USER>/.arduino15/packages/esp32/tools/esp-x32/2411/bin/xtensa-esp32s3-elf-g++", "-M<PERSON>", "-c", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/cpp_flags", "-w", "-<PERSON><PERSON>", "-Werror=return-type", "-DF_CPU=240000000L", "-DARDUINO=10607", "-DARDUINO_ESP32S3_DEV", "-DARDUINO_ARCH_ESP32", "-DARDUINO_BOARD=\"ESP32S3_DEV\"", "-DARDUINO_VARIANT=\"esp32s3\"", "-DARDUINO_PARTITION_default", "-DARDUINO_HOST_OS=\"linux\"", "-DARDUINO_FQBN=\"esp32:esp32:esp32s3:UploadSpeed=921600,USBMode=hwcdc,CDCOnBoot=default,MSCOnBoot=default,DFUOnBoot=default,UploadMode=default,CPUFreq=240,FlashMode=qio,FlashSize=4M,PartitionScheme=default,DebugLevel=none,PSRAM=disabled,LoopCore=1,EventsCore=1,EraseFlash=none,JTAGAdapter=default,ZigbeeMode=default\"", "-DESP32=ESP32", "-DCORE_DEBUG_LEVEL=0", "-DARDUINO_RUNNING_CORE=1", "-DARDUINO_EVENT_RUNNING_CORE=1", "-DARDUINO_USB_MODE=1", "-DARDUINO_USB_CDC_ON_BOOT=0", "-DARDUINO_USB_MSC_ON_BOOT=0", "-DARDUINO_USB_DFU_ON_BOOT=0", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/defines", "-I/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta", "-iprefix", "/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/includes", "-I/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/qio_qspi/include", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/variants/esp32s3", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Wire/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/SPI/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WiFi/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Network/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WebServer/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/FS/src", "-I/home/<USER>/Documents/Arduino/libraries/NTPClient", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/SPIFFS/src", "-I/home/<USER>/Documents/Arduino/libraries/ArduinoJson/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/DNSServer/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/AsyncUDP/src", "-I/home/<USER>/Documents/Arduino/libraries/Adafruit_GFX_Library", "-I/home/<USER>/Documents/Arduino/libraries/Adafruit_BusIO", "-I/home/<USER>/Documents/Arduino/libraries/ESP32_HUB75_LED_MATRIX_PANEL_DMA_Display/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/HTTPClient/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/NetworkClientSecure/src", "-I/home/<USER>/Documents/Arduino/libraries/UnixTime/src", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/build_opt.h", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/file_opts", "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/FS/src/FS.cpp", "-o", "/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/libraries/FS/FS.cpp.o"], "file": "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/FS/src/FS.cpp"}, {"directory": "/home/<USER>", "arguments": ["/home/<USER>/.arduino15/packages/esp32/tools/esp-x32/2411/bin/xtensa-esp32s3-elf-g++", "-M<PERSON>", "-c", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/cpp_flags", "-w", "-<PERSON><PERSON>", "-Werror=return-type", "-DF_CPU=240000000L", "-DARDUINO=10607", "-DARDUINO_ESP32S3_DEV", "-DARDUINO_ARCH_ESP32", "-DARDUINO_BOARD=\"ESP32S3_DEV\"", "-DARDUINO_VARIANT=\"esp32s3\"", "-DARDUINO_PARTITION_default", "-DARDUINO_HOST_OS=\"linux\"", "-DARDUINO_FQBN=\"esp32:esp32:esp32s3:UploadSpeed=921600,USBMode=hwcdc,CDCOnBoot=default,MSCOnBoot=default,DFUOnBoot=default,UploadMode=default,CPUFreq=240,FlashMode=qio,FlashSize=4M,PartitionScheme=default,DebugLevel=none,PSRAM=disabled,LoopCore=1,EventsCore=1,EraseFlash=none,JTAGAdapter=default,ZigbeeMode=default\"", "-DESP32=ESP32", "-DCORE_DEBUG_LEVEL=0", "-DARDUINO_RUNNING_CORE=1", "-DARDUINO_EVENT_RUNNING_CORE=1", "-DARDUINO_USB_MODE=1", "-DARDUINO_USB_CDC_ON_BOOT=0", "-DARDUINO_USB_MSC_ON_BOOT=0", "-DARDUINO_USB_DFU_ON_BOOT=0", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/defines", "-I/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta", "-iprefix", "/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/includes", "-I/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/qio_qspi/include", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/variants/esp32s3", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Wire/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/SPI/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WiFi/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Network/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WebServer/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/FS/src", "-I/home/<USER>/Documents/Arduino/libraries/NTPClient", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/SPIFFS/src", "-I/home/<USER>/Documents/Arduino/libraries/ArduinoJson/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/DNSServer/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/AsyncUDP/src", "-I/home/<USER>/Documents/Arduino/libraries/Adafruit_GFX_Library", "-I/home/<USER>/Documents/Arduino/libraries/Adafruit_BusIO", "-I/home/<USER>/Documents/Arduino/libraries/ESP32_HUB75_LED_MATRIX_PANEL_DMA_Display/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/HTTPClient/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/NetworkClientSecure/src", "-I/home/<USER>/Documents/Arduino/libraries/UnixTime/src", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/build_opt.h", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/file_opts", "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/FS/src/vfs_api.cpp", "-o", "/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/libraries/FS/vfs_api.cpp.o"], "file": "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/FS/src/vfs_api.cpp"}, {"directory": "/home/<USER>", "arguments": ["/home/<USER>/.arduino15/packages/esp32/tools/esp-x32/2411/bin/xtensa-esp32s3-elf-g++", "-M<PERSON>", "-c", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/cpp_flags", "-w", "-<PERSON><PERSON>", "-Werror=return-type", "-DF_CPU=240000000L", "-DARDUINO=10607", "-DARDUINO_ESP32S3_DEV", "-DARDUINO_ARCH_ESP32", "-DARDUINO_BOARD=\"ESP32S3_DEV\"", "-DARDUINO_VARIANT=\"esp32s3\"", "-DARDUINO_PARTITION_default", "-DARDUINO_HOST_OS=\"linux\"", "-DARDUINO_FQBN=\"esp32:esp32:esp32s3:UploadSpeed=921600,USBMode=hwcdc,CDCOnBoot=default,MSCOnBoot=default,DFUOnBoot=default,UploadMode=default,CPUFreq=240,FlashMode=qio,FlashSize=4M,PartitionScheme=default,DebugLevel=none,PSRAM=disabled,LoopCore=1,EventsCore=1,EraseFlash=none,JTAGAdapter=default,ZigbeeMode=default\"", "-DESP32=ESP32", "-DCORE_DEBUG_LEVEL=0", "-DARDUINO_RUNNING_CORE=1", "-DARDUINO_EVENT_RUNNING_CORE=1", "-DARDUINO_USB_MODE=1", "-DARDUINO_USB_CDC_ON_BOOT=0", "-DARDUINO_USB_MSC_ON_BOOT=0", "-DARDUINO_USB_DFU_ON_BOOT=0", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/defines", "-I/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta", "-iprefix", "/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/includes", "-I/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/qio_qspi/include", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/variants/esp32s3", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Wire/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/SPI/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WiFi/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Network/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WebServer/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/FS/src", "-I/home/<USER>/Documents/Arduino/libraries/NTPClient", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/SPIFFS/src", "-I/home/<USER>/Documents/Arduino/libraries/ArduinoJson/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/DNSServer/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/AsyncUDP/src", "-I/home/<USER>/Documents/Arduino/libraries/Adafruit_GFX_Library", "-I/home/<USER>/Documents/Arduino/libraries/Adafruit_BusIO", "-I/home/<USER>/Documents/Arduino/libraries/ESP32_HUB75_LED_MATRIX_PANEL_DMA_Display/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/HTTPClient/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/NetworkClientSecure/src", "-I/home/<USER>/Documents/Arduino/libraries/UnixTime/src", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/build_opt.h", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/file_opts", "/home/<USER>/Documents/Arduino/libraries/NTPClient/NTPClient.cpp", "-o", "/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/libraries/NTPClient/NTPClient.cpp.o"], "file": "/home/<USER>/Documents/Arduino/libraries/NTPClient/NTPClient.cpp"}, {"directory": "/home/<USER>", "arguments": ["/home/<USER>/.arduino15/packages/esp32/tools/esp-x32/2411/bin/xtensa-esp32s3-elf-g++", "-M<PERSON>", "-c", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/cpp_flags", "-w", "-<PERSON><PERSON>", "-Werror=return-type", "-DF_CPU=240000000L", "-DARDUINO=10607", "-DARDUINO_ESP32S3_DEV", "-DARDUINO_ARCH_ESP32", "-DARDUINO_BOARD=\"ESP32S3_DEV\"", "-DARDUINO_VARIANT=\"esp32s3\"", "-DARDUINO_PARTITION_default", "-DARDUINO_HOST_OS=\"linux\"", "-DARDUINO_FQBN=\"esp32:esp32:esp32s3:UploadSpeed=921600,USBMode=hwcdc,CDCOnBoot=default,MSCOnBoot=default,DFUOnBoot=default,UploadMode=default,CPUFreq=240,FlashMode=qio,FlashSize=4M,PartitionScheme=default,DebugLevel=none,PSRAM=disabled,LoopCore=1,EventsCore=1,EraseFlash=none,JTAGAdapter=default,ZigbeeMode=default\"", "-DESP32=ESP32", "-DCORE_DEBUG_LEVEL=0", "-DARDUINO_RUNNING_CORE=1", "-DARDUINO_EVENT_RUNNING_CORE=1", "-DARDUINO_USB_MODE=1", "-DARDUINO_USB_CDC_ON_BOOT=0", "-DARDUINO_USB_MSC_ON_BOOT=0", "-DARDUINO_USB_DFU_ON_BOOT=0", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/defines", "-I/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta", "-iprefix", "/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/includes", "-I/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/qio_qspi/include", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/variants/esp32s3", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Wire/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/SPI/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WiFi/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Network/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WebServer/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/FS/src", "-I/home/<USER>/Documents/Arduino/libraries/NTPClient", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/SPIFFS/src", "-I/home/<USER>/Documents/Arduino/libraries/ArduinoJson/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/DNSServer/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/AsyncUDP/src", "-I/home/<USER>/Documents/Arduino/libraries/Adafruit_GFX_Library", "-I/home/<USER>/Documents/Arduino/libraries/Adafruit_BusIO", "-I/home/<USER>/Documents/Arduino/libraries/ESP32_HUB75_LED_MATRIX_PANEL_DMA_Display/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/HTTPClient/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/NetworkClientSecure/src", "-I/home/<USER>/Documents/Arduino/libraries/UnixTime/src", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/build_opt.h", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/file_opts", "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/SPIFFS/src/SPIFFS.cpp", "-o", "/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/libraries/SPIFFS/SPIFFS.cpp.o"], "file": "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/SPIFFS/src/SPIFFS.cpp"}, {"directory": "/home/<USER>", "arguments": ["/home/<USER>/.arduino15/packages/esp32/tools/esp-x32/2411/bin/xtensa-esp32s3-elf-g++", "-M<PERSON>", "-c", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/cpp_flags", "-w", "-<PERSON><PERSON>", "-Werror=return-type", "-DF_CPU=240000000L", "-DARDUINO=10607", "-DARDUINO_ESP32S3_DEV", "-DARDUINO_ARCH_ESP32", "-DARDUINO_BOARD=\"ESP32S3_DEV\"", "-DARDUINO_VARIANT=\"esp32s3\"", "-DARDUINO_PARTITION_default", "-DARDUINO_HOST_OS=\"linux\"", "-DARDUINO_FQBN=\"esp32:esp32:esp32s3:UploadSpeed=921600,USBMode=hwcdc,CDCOnBoot=default,MSCOnBoot=default,DFUOnBoot=default,UploadMode=default,CPUFreq=240,FlashMode=qio,FlashSize=4M,PartitionScheme=default,DebugLevel=none,PSRAM=disabled,LoopCore=1,EventsCore=1,EraseFlash=none,JTAGAdapter=default,ZigbeeMode=default\"", "-DESP32=ESP32", "-DCORE_DEBUG_LEVEL=0", "-DARDUINO_RUNNING_CORE=1", "-DARDUINO_EVENT_RUNNING_CORE=1", "-DARDUINO_USB_MODE=1", "-DARDUINO_USB_CDC_ON_BOOT=0", "-DARDUINO_USB_MSC_ON_BOOT=0", "-DARDUINO_USB_DFU_ON_BOOT=0", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/defines", "-I/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta", "-iprefix", "/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/includes", "-I/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/qio_qspi/include", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/variants/esp32s3", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Wire/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/SPI/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WiFi/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Network/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WebServer/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/FS/src", "-I/home/<USER>/Documents/Arduino/libraries/NTPClient", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/SPIFFS/src", "-I/home/<USER>/Documents/Arduino/libraries/ArduinoJson/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/DNSServer/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/AsyncUDP/src", "-I/home/<USER>/Documents/Arduino/libraries/Adafruit_GFX_Library", "-I/home/<USER>/Documents/Arduino/libraries/Adafruit_BusIO", "-I/home/<USER>/Documents/Arduino/libraries/ESP32_HUB75_LED_MATRIX_PANEL_DMA_Display/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/HTTPClient/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/NetworkClientSecure/src", "-I/home/<USER>/Documents/Arduino/libraries/UnixTime/src", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/build_opt.h", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/file_opts", "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/DNSServer/src/DNSServer.cpp", "-o", "/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/libraries/DNSServer/DNSServer.cpp.o"], "file": "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/DNSServer/src/DNSServer.cpp"}, {"directory": "/home/<USER>", "arguments": ["/home/<USER>/.arduino15/packages/esp32/tools/esp-x32/2411/bin/xtensa-esp32s3-elf-g++", "-M<PERSON>", "-c", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/cpp_flags", "-w", "-<PERSON><PERSON>", "-Werror=return-type", "-DF_CPU=240000000L", "-DARDUINO=10607", "-DARDUINO_ESP32S3_DEV", "-DARDUINO_ARCH_ESP32", "-DARDUINO_BOARD=\"ESP32S3_DEV\"", "-DARDUINO_VARIANT=\"esp32s3\"", "-DARDUINO_PARTITION_default", "-DARDUINO_HOST_OS=\"linux\"", "-DARDUINO_FQBN=\"esp32:esp32:esp32s3:UploadSpeed=921600,USBMode=hwcdc,CDCOnBoot=default,MSCOnBoot=default,DFUOnBoot=default,UploadMode=default,CPUFreq=240,FlashMode=qio,FlashSize=4M,PartitionScheme=default,DebugLevel=none,PSRAM=disabled,LoopCore=1,EventsCore=1,EraseFlash=none,JTAGAdapter=default,ZigbeeMode=default\"", "-DESP32=ESP32", "-DCORE_DEBUG_LEVEL=0", "-DARDUINO_RUNNING_CORE=1", "-DARDUINO_EVENT_RUNNING_CORE=1", "-DARDUINO_USB_MODE=1", "-DARDUINO_USB_CDC_ON_BOOT=0", "-DARDUINO_USB_MSC_ON_BOOT=0", "-DARDUINO_USB_DFU_ON_BOOT=0", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/defines", "-I/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta", "-iprefix", "/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/includes", "-I/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/qio_qspi/include", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/variants/esp32s3", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Wire/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/SPI/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WiFi/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Network/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WebServer/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/FS/src", "-I/home/<USER>/Documents/Arduino/libraries/NTPClient", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/SPIFFS/src", "-I/home/<USER>/Documents/Arduino/libraries/ArduinoJson/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/DNSServer/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/AsyncUDP/src", "-I/home/<USER>/Documents/Arduino/libraries/Adafruit_GFX_Library", "-I/home/<USER>/Documents/Arduino/libraries/Adafruit_BusIO", "-I/home/<USER>/Documents/Arduino/libraries/ESP32_HUB75_LED_MATRIX_PANEL_DMA_Display/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/HTTPClient/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/NetworkClientSecure/src", "-I/home/<USER>/Documents/Arduino/libraries/UnixTime/src", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/build_opt.h", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/file_opts", "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/AsyncUDP/src/AsyncUDP.cpp", "-o", "/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/libraries/AsyncUDP/AsyncUDP.cpp.o"], "file": "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/AsyncUDP/src/AsyncUDP.cpp"}, {"directory": "/home/<USER>", "arguments": ["/home/<USER>/.arduino15/packages/esp32/tools/esp-x32/2411/bin/xtensa-esp32s3-elf-g++", "-M<PERSON>", "-c", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/cpp_flags", "-w", "-<PERSON><PERSON>", "-Werror=return-type", "-DF_CPU=240000000L", "-DARDUINO=10607", "-DARDUINO_ESP32S3_DEV", "-DARDUINO_ARCH_ESP32", "-DARDUINO_BOARD=\"ESP32S3_DEV\"", "-DARDUINO_VARIANT=\"esp32s3\"", "-DARDUINO_PARTITION_default", "-DARDUINO_HOST_OS=\"linux\"", "-DARDUINO_FQBN=\"esp32:esp32:esp32s3:UploadSpeed=921600,USBMode=hwcdc,CDCOnBoot=default,MSCOnBoot=default,DFUOnBoot=default,UploadMode=default,CPUFreq=240,FlashMode=qio,FlashSize=4M,PartitionScheme=default,DebugLevel=none,PSRAM=disabled,LoopCore=1,EventsCore=1,EraseFlash=none,JTAGAdapter=default,ZigbeeMode=default\"", "-DESP32=ESP32", "-DCORE_DEBUG_LEVEL=0", "-DARDUINO_RUNNING_CORE=1", "-DARDUINO_EVENT_RUNNING_CORE=1", "-DARDUINO_USB_MODE=1", "-DARDUINO_USB_CDC_ON_BOOT=0", "-DARDUINO_USB_MSC_ON_BOOT=0", "-DARDUINO_USB_DFU_ON_BOOT=0", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/defines", "-I/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta", "-iprefix", "/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/includes", "-I/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/qio_qspi/include", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/variants/esp32s3", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Wire/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/SPI/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WiFi/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Network/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WebServer/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/FS/src", "-I/home/<USER>/Documents/Arduino/libraries/NTPClient", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/SPIFFS/src", "-I/home/<USER>/Documents/Arduino/libraries/ArduinoJson/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/DNSServer/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/AsyncUDP/src", "-I/home/<USER>/Documents/Arduino/libraries/Adafruit_GFX_Library", "-I/home/<USER>/Documents/Arduino/libraries/Adafruit_BusIO", "-I/home/<USER>/Documents/Arduino/libraries/ESP32_HUB75_LED_MATRIX_PANEL_DMA_Display/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/HTTPClient/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/NetworkClientSecure/src", "-I/home/<USER>/Documents/Arduino/libraries/UnixTime/src", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/build_opt.h", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/file_opts", "/home/<USER>/Documents/Arduino/libraries/Adafruit_GFX_Library/Adafruit_GrayOLED.cpp", "-o", "/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/libraries/Adafruit_GFX_Library/Adafruit_GrayOLED.cpp.o"], "file": "/home/<USER>/Documents/Arduino/libraries/Adafruit_GFX_Library/Adafruit_GrayOLED.cpp"}, {"directory": "/home/<USER>", "arguments": ["/home/<USER>/.arduino15/packages/esp32/tools/esp-x32/2411/bin/xtensa-esp32s3-elf-g++", "-M<PERSON>", "-c", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/cpp_flags", "-w", "-<PERSON><PERSON>", "-Werror=return-type", "-DF_CPU=240000000L", "-DARDUINO=10607", "-DARDUINO_ESP32S3_DEV", "-DARDUINO_ARCH_ESP32", "-DARDUINO_BOARD=\"ESP32S3_DEV\"", "-DARDUINO_VARIANT=\"esp32s3\"", "-DARDUINO_PARTITION_default", "-DARDUINO_HOST_OS=\"linux\"", "-DARDUINO_FQBN=\"esp32:esp32:esp32s3:UploadSpeed=921600,USBMode=hwcdc,CDCOnBoot=default,MSCOnBoot=default,DFUOnBoot=default,UploadMode=default,CPUFreq=240,FlashMode=qio,FlashSize=4M,PartitionScheme=default,DebugLevel=none,PSRAM=disabled,LoopCore=1,EventsCore=1,EraseFlash=none,JTAGAdapter=default,ZigbeeMode=default\"", "-DESP32=ESP32", "-DCORE_DEBUG_LEVEL=0", "-DARDUINO_RUNNING_CORE=1", "-DARDUINO_EVENT_RUNNING_CORE=1", "-DARDUINO_USB_MODE=1", "-DARDUINO_USB_CDC_ON_BOOT=0", "-DARDUINO_USB_MSC_ON_BOOT=0", "-DARDUINO_USB_DFU_ON_BOOT=0", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/defines", "-I/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta", "-iprefix", "/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/includes", "-I/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/qio_qspi/include", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/variants/esp32s3", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Wire/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/SPI/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WiFi/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Network/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WebServer/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/FS/src", "-I/home/<USER>/Documents/Arduino/libraries/NTPClient", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/SPIFFS/src", "-I/home/<USER>/Documents/Arduino/libraries/ArduinoJson/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/DNSServer/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/AsyncUDP/src", "-I/home/<USER>/Documents/Arduino/libraries/Adafruit_GFX_Library", "-I/home/<USER>/Documents/Arduino/libraries/Adafruit_BusIO", "-I/home/<USER>/Documents/Arduino/libraries/ESP32_HUB75_LED_MATRIX_PANEL_DMA_Display/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/HTTPClient/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/NetworkClientSecure/src", "-I/home/<USER>/Documents/Arduino/libraries/UnixTime/src", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/build_opt.h", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/file_opts", "/home/<USER>/Documents/Arduino/libraries/Adafruit_GFX_Library/Adafruit_GFX.cpp", "-o", "/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/libraries/Adafruit_GFX_Library/Adafruit_GFX.cpp.o"], "file": "/home/<USER>/Documents/Arduino/libraries/Adafruit_GFX_Library/Adafruit_GFX.cpp"}, {"directory": "/home/<USER>", "arguments": ["/home/<USER>/.arduino15/packages/esp32/tools/esp-x32/2411/bin/xtensa-esp32s3-elf-g++", "-M<PERSON>", "-c", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/cpp_flags", "-w", "-<PERSON><PERSON>", "-Werror=return-type", "-DF_CPU=240000000L", "-DARDUINO=10607", "-DARDUINO_ESP32S3_DEV", "-DARDUINO_ARCH_ESP32", "-DARDUINO_BOARD=\"ESP32S3_DEV\"", "-DARDUINO_VARIANT=\"esp32s3\"", "-DARDUINO_PARTITION_default", "-DARDUINO_HOST_OS=\"linux\"", "-DARDUINO_FQBN=\"esp32:esp32:esp32s3:UploadSpeed=921600,USBMode=hwcdc,CDCOnBoot=default,MSCOnBoot=default,DFUOnBoot=default,UploadMode=default,CPUFreq=240,FlashMode=qio,FlashSize=4M,PartitionScheme=default,DebugLevel=none,PSRAM=disabled,LoopCore=1,EventsCore=1,EraseFlash=none,JTAGAdapter=default,ZigbeeMode=default\"", "-DESP32=ESP32", "-DCORE_DEBUG_LEVEL=0", "-DARDUINO_RUNNING_CORE=1", "-DARDUINO_EVENT_RUNNING_CORE=1", "-DARDUINO_USB_MODE=1", "-DARDUINO_USB_CDC_ON_BOOT=0", "-DARDUINO_USB_MSC_ON_BOOT=0", "-DARDUINO_USB_DFU_ON_BOOT=0", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/defines", "-I/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta", "-iprefix", "/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/includes", "-I/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/qio_qspi/include", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/variants/esp32s3", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Wire/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/SPI/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WiFi/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Network/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WebServer/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/FS/src", "-I/home/<USER>/Documents/Arduino/libraries/NTPClient", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/SPIFFS/src", "-I/home/<USER>/Documents/Arduino/libraries/ArduinoJson/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/DNSServer/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/AsyncUDP/src", "-I/home/<USER>/Documents/Arduino/libraries/Adafruit_GFX_Library", "-I/home/<USER>/Documents/Arduino/libraries/Adafruit_BusIO", "-I/home/<USER>/Documents/Arduino/libraries/ESP32_HUB75_LED_MATRIX_PANEL_DMA_Display/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/HTTPClient/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/NetworkClientSecure/src", "-I/home/<USER>/Documents/Arduino/libraries/UnixTime/src", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/build_opt.h", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/file_opts", "/home/<USER>/Documents/Arduino/libraries/Adafruit_GFX_Library/Adafruit_SPITFT.cpp", "-o", "/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/libraries/Adafruit_GFX_Library/Adafruit_SPITFT.cpp.o"], "file": "/home/<USER>/Documents/Arduino/libraries/Adafruit_GFX_Library/Adafruit_SPITFT.cpp"}, {"directory": "/home/<USER>", "arguments": ["/home/<USER>/.arduino15/packages/esp32/tools/esp-x32/2411/bin/xtensa-esp32s3-elf-gcc", "-M<PERSON>", "-c", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/c_flags", "-w", "-<PERSON><PERSON>", "-Werror=return-type", "-DF_CPU=240000000L", "-DARDUINO=10607", "-DARDUINO_ESP32S3_DEV", "-DARDUINO_ARCH_ESP32", "-DARDUINO_BOARD=\"ESP32S3_DEV\"", "-DARDUINO_VARIANT=\"esp32s3\"", "-DARDUINO_PARTITION_default", "-DARDUINO_HOST_OS=\"linux\"", "-DARDUINO_FQBN=\"esp32:esp32:esp32s3:UploadSpeed=921600,USBMode=hwcdc,CDCOnBoot=default,MSCOnBoot=default,DFUOnBoot=default,UploadMode=default,CPUFreq=240,FlashMode=qio,FlashSize=4M,PartitionScheme=default,DebugLevel=none,PSRAM=disabled,LoopCore=1,EventsCore=1,EraseFlash=none,JTAGAdapter=default,ZigbeeMode=default\"", "-DESP32=ESP32", "-DCORE_DEBUG_LEVEL=0", "-DARDUINO_RUNNING_CORE=1", "-DARDUINO_EVENT_RUNNING_CORE=1", "-DARDUINO_USB_MODE=1", "-DARDUINO_USB_CDC_ON_BOOT=0", "-DARDUINO_USB_MSC_ON_BOOT=0", "-DARDUINO_USB_DFU_ON_BOOT=0", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/defines", "-I/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta", "-iprefix", "/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/includes", "-I/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/qio_qspi/include", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/variants/esp32s3", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Wire/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/SPI/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WiFi/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Network/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WebServer/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/FS/src", "-I/home/<USER>/Documents/Arduino/libraries/NTPClient", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/SPIFFS/src", "-I/home/<USER>/Documents/Arduino/libraries/ArduinoJson/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/DNSServer/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/AsyncUDP/src", "-I/home/<USER>/Documents/Arduino/libraries/Adafruit_GFX_Library", "-I/home/<USER>/Documents/Arduino/libraries/Adafruit_BusIO", "-I/home/<USER>/Documents/Arduino/libraries/ESP32_HUB75_LED_MATRIX_PANEL_DMA_Display/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/HTTPClient/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/NetworkClientSecure/src", "-I/home/<USER>/Documents/Arduino/libraries/UnixTime/src", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/build_opt.h", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/file_opts", "/home/<USER>/Documents/Arduino/libraries/Adafruit_GFX_Library/glcdfont.c", "-o", "/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/libraries/Adafruit_GFX_Library/glcdfont.c.o"], "file": "/home/<USER>/Documents/Arduino/libraries/Adafruit_GFX_Library/glcdfont.c"}, {"directory": "/home/<USER>", "arguments": ["/home/<USER>/.arduino15/packages/esp32/tools/esp-x32/2411/bin/xtensa-esp32s3-elf-g++", "-M<PERSON>", "-c", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/cpp_flags", "-w", "-<PERSON><PERSON>", "-Werror=return-type", "-DF_CPU=240000000L", "-DARDUINO=10607", "-DARDUINO_ESP32S3_DEV", "-DARDUINO_ARCH_ESP32", "-DARDUINO_BOARD=\"ESP32S3_DEV\"", "-DARDUINO_VARIANT=\"esp32s3\"", "-DARDUINO_PARTITION_default", "-DARDUINO_HOST_OS=\"linux\"", "-DARDUINO_FQBN=\"esp32:esp32:esp32s3:UploadSpeed=921600,USBMode=hwcdc,CDCOnBoot=default,MSCOnBoot=default,DFUOnBoot=default,UploadMode=default,CPUFreq=240,FlashMode=qio,FlashSize=4M,PartitionScheme=default,DebugLevel=none,PSRAM=disabled,LoopCore=1,EventsCore=1,EraseFlash=none,JTAGAdapter=default,ZigbeeMode=default\"", "-DESP32=ESP32", "-DCORE_DEBUG_LEVEL=0", "-DARDUINO_RUNNING_CORE=1", "-DARDUINO_EVENT_RUNNING_CORE=1", "-DARDUINO_USB_MODE=1", "-DARDUINO_USB_CDC_ON_BOOT=0", "-DARDUINO_USB_MSC_ON_BOOT=0", "-DARDUINO_USB_DFU_ON_BOOT=0", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/defines", "-I/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta", "-iprefix", "/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/includes", "-I/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/qio_qspi/include", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/variants/esp32s3", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Wire/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/SPI/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WiFi/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Network/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WebServer/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/FS/src", "-I/home/<USER>/Documents/Arduino/libraries/NTPClient", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/SPIFFS/src", "-I/home/<USER>/Documents/Arduino/libraries/ArduinoJson/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/DNSServer/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/AsyncUDP/src", "-I/home/<USER>/Documents/Arduino/libraries/Adafruit_GFX_Library", "-I/home/<USER>/Documents/Arduino/libraries/Adafruit_BusIO", "-I/home/<USER>/Documents/Arduino/libraries/ESP32_HUB75_LED_MATRIX_PANEL_DMA_Display/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/HTTPClient/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/NetworkClientSecure/src", "-I/home/<USER>/Documents/Arduino/libraries/UnixTime/src", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/build_opt.h", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/file_opts", "/home/<USER>/Documents/Arduino/libraries/Adafruit_BusIO/Adafruit_SPIDevice.cpp", "-o", "/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/libraries/Adafruit_BusIO/Adafruit_SPIDevice.cpp.o"], "file": "/home/<USER>/Documents/Arduino/libraries/Adafruit_BusIO/Adafruit_SPIDevice.cpp"}, {"directory": "/home/<USER>", "arguments": ["/home/<USER>/.arduino15/packages/esp32/tools/esp-x32/2411/bin/xtensa-esp32s3-elf-g++", "-M<PERSON>", "-c", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/cpp_flags", "-w", "-<PERSON><PERSON>", "-Werror=return-type", "-DF_CPU=240000000L", "-DARDUINO=10607", "-DARDUINO_ESP32S3_DEV", "-DARDUINO_ARCH_ESP32", "-DARDUINO_BOARD=\"ESP32S3_DEV\"", "-DARDUINO_VARIANT=\"esp32s3\"", "-DARDUINO_PARTITION_default", "-DARDUINO_HOST_OS=\"linux\"", "-DARDUINO_FQBN=\"esp32:esp32:esp32s3:UploadSpeed=921600,USBMode=hwcdc,CDCOnBoot=default,MSCOnBoot=default,DFUOnBoot=default,UploadMode=default,CPUFreq=240,FlashMode=qio,FlashSize=4M,PartitionScheme=default,DebugLevel=none,PSRAM=disabled,LoopCore=1,EventsCore=1,EraseFlash=none,JTAGAdapter=default,ZigbeeMode=default\"", "-DESP32=ESP32", "-DCORE_DEBUG_LEVEL=0", "-DARDUINO_RUNNING_CORE=1", "-DARDUINO_EVENT_RUNNING_CORE=1", "-DARDUINO_USB_MODE=1", "-DARDUINO_USB_CDC_ON_BOOT=0", "-DARDUINO_USB_MSC_ON_BOOT=0", "-DARDUINO_USB_DFU_ON_BOOT=0", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/defines", "-I/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta", "-iprefix", "/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/includes", "-I/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/qio_qspi/include", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/variants/esp32s3", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Wire/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/SPI/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WiFi/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Network/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WebServer/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/FS/src", "-I/home/<USER>/Documents/Arduino/libraries/NTPClient", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/SPIFFS/src", "-I/home/<USER>/Documents/Arduino/libraries/ArduinoJson/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/DNSServer/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/AsyncUDP/src", "-I/home/<USER>/Documents/Arduino/libraries/Adafruit_GFX_Library", "-I/home/<USER>/Documents/Arduino/libraries/Adafruit_BusIO", "-I/home/<USER>/Documents/Arduino/libraries/ESP32_HUB75_LED_MATRIX_PANEL_DMA_Display/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/HTTPClient/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/NetworkClientSecure/src", "-I/home/<USER>/Documents/Arduino/libraries/UnixTime/src", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/build_opt.h", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/file_opts", "/home/<USER>/Documents/Arduino/libraries/Adafruit_BusIO/Adafruit_BusIO_Register.cpp", "-o", "/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/libraries/Adafruit_BusIO/Adafruit_BusIO_Register.cpp.o"], "file": "/home/<USER>/Documents/Arduino/libraries/Adafruit_BusIO/Adafruit_BusIO_Register.cpp"}, {"directory": "/home/<USER>", "arguments": ["/home/<USER>/.arduino15/packages/esp32/tools/esp-x32/2411/bin/xtensa-esp32s3-elf-g++", "-M<PERSON>", "-c", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/cpp_flags", "-w", "-<PERSON><PERSON>", "-Werror=return-type", "-DF_CPU=240000000L", "-DARDUINO=10607", "-DARDUINO_ESP32S3_DEV", "-DARDUINO_ARCH_ESP32", "-DARDUINO_BOARD=\"ESP32S3_DEV\"", "-DARDUINO_VARIANT=\"esp32s3\"", "-DARDUINO_PARTITION_default", "-DARDUINO_HOST_OS=\"linux\"", "-DARDUINO_FQBN=\"esp32:esp32:esp32s3:UploadSpeed=921600,USBMode=hwcdc,CDCOnBoot=default,MSCOnBoot=default,DFUOnBoot=default,UploadMode=default,CPUFreq=240,FlashMode=qio,FlashSize=4M,PartitionScheme=default,DebugLevel=none,PSRAM=disabled,LoopCore=1,EventsCore=1,EraseFlash=none,JTAGAdapter=default,ZigbeeMode=default\"", "-DESP32=ESP32", "-DCORE_DEBUG_LEVEL=0", "-DARDUINO_RUNNING_CORE=1", "-DARDUINO_EVENT_RUNNING_CORE=1", "-DARDUINO_USB_MODE=1", "-DARDUINO_USB_CDC_ON_BOOT=0", "-DARDUINO_USB_MSC_ON_BOOT=0", "-DARDUINO_USB_DFU_ON_BOOT=0", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/defines", "-I/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta", "-iprefix", "/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/includes", "-I/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/qio_qspi/include", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/variants/esp32s3", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Wire/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/SPI/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WiFi/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Network/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WebServer/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/FS/src", "-I/home/<USER>/Documents/Arduino/libraries/NTPClient", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/SPIFFS/src", "-I/home/<USER>/Documents/Arduino/libraries/ArduinoJson/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/DNSServer/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/AsyncUDP/src", "-I/home/<USER>/Documents/Arduino/libraries/Adafruit_GFX_Library", "-I/home/<USER>/Documents/Arduino/libraries/Adafruit_BusIO", "-I/home/<USER>/Documents/Arduino/libraries/ESP32_HUB75_LED_MATRIX_PANEL_DMA_Display/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/HTTPClient/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/NetworkClientSecure/src", "-I/home/<USER>/Documents/Arduino/libraries/UnixTime/src", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/build_opt.h", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/file_opts", "/home/<USER>/Documents/Arduino/libraries/Adafruit_BusIO/Adafruit_I2CDevice.cpp", "-o", "/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/libraries/Adafruit_BusIO/Adafruit_I2CDevice.cpp.o"], "file": "/home/<USER>/Documents/Arduino/libraries/Adafruit_BusIO/Adafruit_I2CDevice.cpp"}, {"directory": "/home/<USER>", "arguments": ["/home/<USER>/.arduino15/packages/esp32/tools/esp-x32/2411/bin/xtensa-esp32s3-elf-g++", "-M<PERSON>", "-c", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/cpp_flags", "-w", "-<PERSON><PERSON>", "-Werror=return-type", "-DF_CPU=240000000L", "-DARDUINO=10607", "-DARDUINO_ESP32S3_DEV", "-DARDUINO_ARCH_ESP32", "-DARDUINO_BOARD=\"ESP32S3_DEV\"", "-DARDUINO_VARIANT=\"esp32s3\"", "-DARDUINO_PARTITION_default", "-DARDUINO_HOST_OS=\"linux\"", "-DARDUINO_FQBN=\"esp32:esp32:esp32s3:UploadSpeed=921600,USBMode=hwcdc,CDCOnBoot=default,MSCOnBoot=default,DFUOnBoot=default,UploadMode=default,CPUFreq=240,FlashMode=qio,FlashSize=4M,PartitionScheme=default,DebugLevel=none,PSRAM=disabled,LoopCore=1,EventsCore=1,EraseFlash=none,JTAGAdapter=default,ZigbeeMode=default\"", "-DESP32=ESP32", "-DCORE_DEBUG_LEVEL=0", "-DARDUINO_RUNNING_CORE=1", "-DARDUINO_EVENT_RUNNING_CORE=1", "-DARDUINO_USB_MODE=1", "-DARDUINO_USB_CDC_ON_BOOT=0", "-DARDUINO_USB_MSC_ON_BOOT=0", "-DARDUINO_USB_DFU_ON_BOOT=0", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/defines", "-I/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta", "-iprefix", "/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/includes", "-I/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/qio_qspi/include", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/variants/esp32s3", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Wire/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/SPI/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WiFi/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Network/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WebServer/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/FS/src", "-I/home/<USER>/Documents/Arduino/libraries/NTPClient", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/SPIFFS/src", "-I/home/<USER>/Documents/Arduino/libraries/ArduinoJson/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/DNSServer/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/AsyncUDP/src", "-I/home/<USER>/Documents/Arduino/libraries/Adafruit_GFX_Library", "-I/home/<USER>/Documents/Arduino/libraries/Adafruit_BusIO", "-I/home/<USER>/Documents/Arduino/libraries/ESP32_HUB75_LED_MATRIX_PANEL_DMA_Display/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/HTTPClient/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/NetworkClientSecure/src", "-I/home/<USER>/Documents/Arduino/libraries/UnixTime/src", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/build_opt.h", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/file_opts", "/home/<USER>/Documents/Arduino/libraries/Adafruit_BusIO/Adafruit_GenericDevice.cpp", "-o", "/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/libraries/Adafruit_BusIO/Adafruit_GenericDevice.cpp.o"], "file": "/home/<USER>/Documents/Arduino/libraries/Adafruit_BusIO/Adafruit_GenericDevice.cpp"}, {"directory": "/home/<USER>", "arguments": ["/home/<USER>/.arduino15/packages/esp32/tools/esp-x32/2411/bin/xtensa-esp32s3-elf-g++", "-M<PERSON>", "-c", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/cpp_flags", "-w", "-<PERSON><PERSON>", "-Werror=return-type", "-DF_CPU=240000000L", "-DARDUINO=10607", "-DARDUINO_ESP32S3_DEV", "-DARDUINO_ARCH_ESP32", "-DARDUINO_BOARD=\"ESP32S3_DEV\"", "-DARDUINO_VARIANT=\"esp32s3\"", "-DARDUINO_PARTITION_default", "-DARDUINO_HOST_OS=\"linux\"", "-DARDUINO_FQBN=\"esp32:esp32:esp32s3:UploadSpeed=921600,USBMode=hwcdc,CDCOnBoot=default,MSCOnBoot=default,DFUOnBoot=default,UploadMode=default,CPUFreq=240,FlashMode=qio,FlashSize=4M,PartitionScheme=default,DebugLevel=none,PSRAM=disabled,LoopCore=1,EventsCore=1,EraseFlash=none,JTAGAdapter=default,ZigbeeMode=default\"", "-DESP32=ESP32", "-DCORE_DEBUG_LEVEL=0", "-DARDUINO_RUNNING_CORE=1", "-DARDUINO_EVENT_RUNNING_CORE=1", "-DARDUINO_USB_MODE=1", "-DARDUINO_USB_CDC_ON_BOOT=0", "-DARDUINO_USB_MSC_ON_BOOT=0", "-DARDUINO_USB_DFU_ON_BOOT=0", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/defines", "-I/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta", "-iprefix", "/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/includes", "-I/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/qio_qspi/include", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/variants/esp32s3", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Wire/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/SPI/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WiFi/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Network/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WebServer/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/FS/src", "-I/home/<USER>/Documents/Arduino/libraries/NTPClient", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/SPIFFS/src", "-I/home/<USER>/Documents/Arduino/libraries/ArduinoJson/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/DNSServer/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/AsyncUDP/src", "-I/home/<USER>/Documents/Arduino/libraries/Adafruit_GFX_Library", "-I/home/<USER>/Documents/Arduino/libraries/Adafruit_BusIO", "-I/home/<USER>/Documents/Arduino/libraries/ESP32_HUB75_LED_MATRIX_PANEL_DMA_Display/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/HTTPClient/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/NetworkClientSecure/src", "-I/home/<USER>/Documents/Arduino/libraries/UnixTime/src", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/build_opt.h", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/file_opts", "/home/<USER>/Documents/Arduino/libraries/ESP32_HUB75_LED_MATRIX_PANEL_DMA_Display/src/ESP32-HUB75-MatrixPanel-leddrivers.cpp", "-o", "/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/libraries/ESP32_HUB75_LED_MATRIX_PANEL_DMA_Display/ESP32-HUB75-MatrixPanel-leddrivers.cpp.o"], "file": "/home/<USER>/Documents/Arduino/libraries/ESP32_HUB75_LED_MATRIX_PANEL_DMA_Display/src/ESP32-HUB75-MatrixPanel-leddrivers.cpp"}, {"directory": "/home/<USER>", "arguments": ["/home/<USER>/.arduino15/packages/esp32/tools/esp-x32/2411/bin/xtensa-esp32s3-elf-g++", "-M<PERSON>", "-c", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/cpp_flags", "-w", "-<PERSON><PERSON>", "-Werror=return-type", "-DF_CPU=240000000L", "-DARDUINO=10607", "-DARDUINO_ESP32S3_DEV", "-DARDUINO_ARCH_ESP32", "-DARDUINO_BOARD=\"ESP32S3_DEV\"", "-DARDUINO_VARIANT=\"esp32s3\"", "-DARDUINO_PARTITION_default", "-DARDUINO_HOST_OS=\"linux\"", "-DARDUINO_FQBN=\"esp32:esp32:esp32s3:UploadSpeed=921600,USBMode=hwcdc,CDCOnBoot=default,MSCOnBoot=default,DFUOnBoot=default,UploadMode=default,CPUFreq=240,FlashMode=qio,FlashSize=4M,PartitionScheme=default,DebugLevel=none,PSRAM=disabled,LoopCore=1,EventsCore=1,EraseFlash=none,JTAGAdapter=default,ZigbeeMode=default\"", "-DESP32=ESP32", "-DCORE_DEBUG_LEVEL=0", "-DARDUINO_RUNNING_CORE=1", "-DARDUINO_EVENT_RUNNING_CORE=1", "-DARDUINO_USB_MODE=1", "-DARDUINO_USB_CDC_ON_BOOT=0", "-DARDUINO_USB_MSC_ON_BOOT=0", "-DARDUINO_USB_DFU_ON_BOOT=0", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/defines", "-I/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta", "-iprefix", "/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/includes", "-I/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/qio_qspi/include", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/variants/esp32s3", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Wire/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/SPI/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WiFi/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Network/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WebServer/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/FS/src", "-I/home/<USER>/Documents/Arduino/libraries/NTPClient", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/SPIFFS/src", "-I/home/<USER>/Documents/Arduino/libraries/ArduinoJson/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/DNSServer/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/AsyncUDP/src", "-I/home/<USER>/Documents/Arduino/libraries/Adafruit_GFX_Library", "-I/home/<USER>/Documents/Arduino/libraries/Adafruit_BusIO", "-I/home/<USER>/Documents/Arduino/libraries/ESP32_HUB75_LED_MATRIX_PANEL_DMA_Display/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/HTTPClient/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/NetworkClientSecure/src", "-I/home/<USER>/Documents/Arduino/libraries/UnixTime/src", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/build_opt.h", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/file_opts", "/home/<USER>/Documents/Arduino/libraries/ESP32_HUB75_LED_MATRIX_PANEL_DMA_Display/src/platforms/esp32s3/gdma_lcd_parallel16.cpp", "-o", "/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/libraries/ESP32_HUB75_LED_MATRIX_PANEL_DMA_Display/platforms/esp32s3/gdma_lcd_parallel16.cpp.o"], "file": "/home/<USER>/Documents/Arduino/libraries/ESP32_HUB75_LED_MATRIX_PANEL_DMA_Display/src/platforms/esp32s3/gdma_lcd_parallel16.cpp"}, {"directory": "/home/<USER>", "arguments": ["/home/<USER>/.arduino15/packages/esp32/tools/esp-x32/2411/bin/xtensa-esp32s3-elf-g++", "-M<PERSON>", "-c", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/cpp_flags", "-w", "-<PERSON><PERSON>", "-Werror=return-type", "-DF_CPU=240000000L", "-DARDUINO=10607", "-DARDUINO_ESP32S3_DEV", "-DARDUINO_ARCH_ESP32", "-DARDUINO_BOARD=\"ESP32S3_DEV\"", "-DARDUINO_VARIANT=\"esp32s3\"", "-DARDUINO_PARTITION_default", "-DARDUINO_HOST_OS=\"linux\"", "-DARDUINO_FQBN=\"esp32:esp32:esp32s3:UploadSpeed=921600,USBMode=hwcdc,CDCOnBoot=default,MSCOnBoot=default,DFUOnBoot=default,UploadMode=default,CPUFreq=240,FlashMode=qio,FlashSize=4M,PartitionScheme=default,DebugLevel=none,PSRAM=disabled,LoopCore=1,EventsCore=1,EraseFlash=none,JTAGAdapter=default,ZigbeeMode=default\"", "-DESP32=ESP32", "-DCORE_DEBUG_LEVEL=0", "-DARDUINO_RUNNING_CORE=1", "-DARDUINO_EVENT_RUNNING_CORE=1", "-DARDUINO_USB_MODE=1", "-DARDUINO_USB_CDC_ON_BOOT=0", "-DARDUINO_USB_MSC_ON_BOOT=0", "-DARDUINO_USB_DFU_ON_BOOT=0", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/defines", "-I/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta", "-iprefix", "/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/includes", "-I/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/qio_qspi/include", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/variants/esp32s3", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Wire/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/SPI/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WiFi/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Network/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WebServer/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/FS/src", "-I/home/<USER>/Documents/Arduino/libraries/NTPClient", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/SPIFFS/src", "-I/home/<USER>/Documents/Arduino/libraries/ArduinoJson/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/DNSServer/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/AsyncUDP/src", "-I/home/<USER>/Documents/Arduino/libraries/Adafruit_GFX_Library", "-I/home/<USER>/Documents/Arduino/libraries/Adafruit_BusIO", "-I/home/<USER>/Documents/Arduino/libraries/ESP32_HUB75_LED_MATRIX_PANEL_DMA_Display/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/HTTPClient/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/NetworkClientSecure/src", "-I/home/<USER>/Documents/Arduino/libraries/UnixTime/src", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/build_opt.h", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/file_opts", "/home/<USER>/Documents/Arduino/libraries/ESP32_HUB75_LED_MATRIX_PANEL_DMA_Display/src/platforms/esp32/esp32_i2s_parallel_dma.cpp", "-o", "/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/libraries/ESP32_HUB75_LED_MATRIX_PANEL_DMA_Display/platforms/esp32/esp32_i2s_parallel_dma.cpp.o"], "file": "/home/<USER>/Documents/Arduino/libraries/ESP32_HUB75_LED_MATRIX_PANEL_DMA_Display/src/platforms/esp32/esp32_i2s_parallel_dma.cpp"}, {"directory": "/home/<USER>", "arguments": ["/home/<USER>/.arduino15/packages/esp32/tools/esp-x32/2411/bin/xtensa-esp32s3-elf-g++", "-M<PERSON>", "-c", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/cpp_flags", "-w", "-<PERSON><PERSON>", "-Werror=return-type", "-DF_CPU=240000000L", "-DARDUINO=10607", "-DARDUINO_ESP32S3_DEV", "-DARDUINO_ARCH_ESP32", "-DARDUINO_BOARD=\"ESP32S3_DEV\"", "-DARDUINO_VARIANT=\"esp32s3\"", "-DARDUINO_PARTITION_default", "-DARDUINO_HOST_OS=\"linux\"", "-DARDUINO_FQBN=\"esp32:esp32:esp32s3:UploadSpeed=921600,USBMode=hwcdc,CDCOnBoot=default,MSCOnBoot=default,DFUOnBoot=default,UploadMode=default,CPUFreq=240,FlashMode=qio,FlashSize=4M,PartitionScheme=default,DebugLevel=none,PSRAM=disabled,LoopCore=1,EventsCore=1,EraseFlash=none,JTAGAdapter=default,ZigbeeMode=default\"", "-DESP32=ESP32", "-DCORE_DEBUG_LEVEL=0", "-DARDUINO_RUNNING_CORE=1", "-DARDUINO_EVENT_RUNNING_CORE=1", "-DARDUINO_USB_MODE=1", "-DARDUINO_USB_CDC_ON_BOOT=0", "-DARDUINO_USB_MSC_ON_BOOT=0", "-DARDUINO_USB_DFU_ON_BOOT=0", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/defines", "-I/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta", "-iprefix", "/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/includes", "-I/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/qio_qspi/include", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/variants/esp32s3", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Wire/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/SPI/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WiFi/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Network/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WebServer/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/FS/src", "-I/home/<USER>/Documents/Arduino/libraries/NTPClient", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/SPIFFS/src", "-I/home/<USER>/Documents/Arduino/libraries/ArduinoJson/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/DNSServer/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/AsyncUDP/src", "-I/home/<USER>/Documents/Arduino/libraries/Adafruit_GFX_Library", "-I/home/<USER>/Documents/Arduino/libraries/Adafruit_BusIO", "-I/home/<USER>/Documents/Arduino/libraries/ESP32_HUB75_LED_MATRIX_PANEL_DMA_Display/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/HTTPClient/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/NetworkClientSecure/src", "-I/home/<USER>/Documents/Arduino/libraries/UnixTime/src", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/build_opt.h", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/file_opts", "/home/<USER>/Documents/Arduino/libraries/ESP32_HUB75_LED_MATRIX_PANEL_DMA_Display/src/platforms/esp32c6/dma_parallel_io.cpp", "-o", "/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/libraries/ESP32_HUB75_LED_MATRIX_PANEL_DMA_Display/platforms/esp32c6/dma_parallel_io.cpp.o"], "file": "/home/<USER>/Documents/Arduino/libraries/ESP32_HUB75_LED_MATRIX_PANEL_DMA_Display/src/platforms/esp32c6/dma_parallel_io.cpp"}, {"directory": "/home/<USER>", "arguments": ["/home/<USER>/.arduino15/packages/esp32/tools/esp-x32/2411/bin/xtensa-esp32s3-elf-g++", "-M<PERSON>", "-c", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/cpp_flags", "-w", "-<PERSON><PERSON>", "-Werror=return-type", "-DF_CPU=240000000L", "-DARDUINO=10607", "-DARDUINO_ESP32S3_DEV", "-DARDUINO_ARCH_ESP32", "-DARDUINO_BOARD=\"ESP32S3_DEV\"", "-DARDUINO_VARIANT=\"esp32s3\"", "-DARDUINO_PARTITION_default", "-DARDUINO_HOST_OS=\"linux\"", "-DARDUINO_FQBN=\"esp32:esp32:esp32s3:UploadSpeed=921600,USBMode=hwcdc,CDCOnBoot=default,MSCOnBoot=default,DFUOnBoot=default,UploadMode=default,CPUFreq=240,FlashMode=qio,FlashSize=4M,PartitionScheme=default,DebugLevel=none,PSRAM=disabled,LoopCore=1,EventsCore=1,EraseFlash=none,JTAGAdapter=default,ZigbeeMode=default\"", "-DESP32=ESP32", "-DCORE_DEBUG_LEVEL=0", "-DARDUINO_RUNNING_CORE=1", "-DARDUINO_EVENT_RUNNING_CORE=1", "-DARDUINO_USB_MODE=1", "-DARDUINO_USB_CDC_ON_BOOT=0", "-DARDUINO_USB_MSC_ON_BOOT=0", "-DARDUINO_USB_DFU_ON_BOOT=0", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/defines", "-I/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta", "-iprefix", "/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/includes", "-I/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/qio_qspi/include", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/variants/esp32s3", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Wire/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/SPI/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WiFi/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Network/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WebServer/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/FS/src", "-I/home/<USER>/Documents/Arduino/libraries/NTPClient", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/SPIFFS/src", "-I/home/<USER>/Documents/Arduino/libraries/ArduinoJson/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/DNSServer/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/AsyncUDP/src", "-I/home/<USER>/Documents/Arduino/libraries/Adafruit_GFX_Library", "-I/home/<USER>/Documents/Arduino/libraries/Adafruit_BusIO", "-I/home/<USER>/Documents/Arduino/libraries/ESP32_HUB75_LED_MATRIX_PANEL_DMA_Display/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/HTTPClient/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/NetworkClientSecure/src", "-I/home/<USER>/Documents/Arduino/libraries/UnixTime/src", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/build_opt.h", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/file_opts", "/home/<USER>/Documents/Arduino/libraries/ESP32_HUB75_LED_MATRIX_PANEL_DMA_Display/src/ESP32-HUB75-MatrixPanel-I2S-DMA.cpp", "-o", "/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/libraries/ESP32_HUB75_LED_MATRIX_PANEL_DMA_Display/ESP32-HUB75-MatrixPanel-I2S-DMA.cpp.o"], "file": "/home/<USER>/Documents/Arduino/libraries/ESP32_HUB75_LED_MATRIX_PANEL_DMA_Display/src/ESP32-HUB75-MatrixPanel-I2S-DMA.cpp"}, {"directory": "/home/<USER>", "arguments": ["/home/<USER>/.arduino15/packages/esp32/tools/esp-x32/2411/bin/xtensa-esp32s3-elf-g++", "-M<PERSON>", "-c", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/cpp_flags", "-w", "-<PERSON><PERSON>", "-Werror=return-type", "-DF_CPU=240000000L", "-DARDUINO=10607", "-DARDUINO_ESP32S3_DEV", "-DARDUINO_ARCH_ESP32", "-DARDUINO_BOARD=\"ESP32S3_DEV\"", "-DARDUINO_VARIANT=\"esp32s3\"", "-DARDUINO_PARTITION_default", "-DARDUINO_HOST_OS=\"linux\"", "-DARDUINO_FQBN=\"esp32:esp32:esp32s3:UploadSpeed=921600,USBMode=hwcdc,CDCOnBoot=default,MSCOnBoot=default,DFUOnBoot=default,UploadMode=default,CPUFreq=240,FlashMode=qio,FlashSize=4M,PartitionScheme=default,DebugLevel=none,PSRAM=disabled,LoopCore=1,EventsCore=1,EraseFlash=none,JTAGAdapter=default,ZigbeeMode=default\"", "-DESP32=ESP32", "-DCORE_DEBUG_LEVEL=0", "-DARDUINO_RUNNING_CORE=1", "-DARDUINO_EVENT_RUNNING_CORE=1", "-DARDUINO_USB_MODE=1", "-DARDUINO_USB_CDC_ON_BOOT=0", "-DARDUINO_USB_MSC_ON_BOOT=0", "-DARDUINO_USB_DFU_ON_BOOT=0", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/defines", "-I/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta", "-iprefix", "/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/includes", "-I/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/qio_qspi/include", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/variants/esp32s3", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Wire/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/SPI/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WiFi/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Network/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WebServer/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/FS/src", "-I/home/<USER>/Documents/Arduino/libraries/NTPClient", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/SPIFFS/src", "-I/home/<USER>/Documents/Arduino/libraries/ArduinoJson/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/DNSServer/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/AsyncUDP/src", "-I/home/<USER>/Documents/Arduino/libraries/Adafruit_GFX_Library", "-I/home/<USER>/Documents/Arduino/libraries/Adafruit_BusIO", "-I/home/<USER>/Documents/Arduino/libraries/ESP32_HUB75_LED_MATRIX_PANEL_DMA_Display/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/HTTPClient/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/NetworkClientSecure/src", "-I/home/<USER>/Documents/Arduino/libraries/UnixTime/src", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/build_opt.h", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/file_opts", "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/HTTPClient/src/HTTPClient.cpp", "-o", "/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/libraries/HTTPClient/HTTPClient.cpp.o"], "file": "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/HTTPClient/src/HTTPClient.cpp"}, {"directory": "/home/<USER>", "arguments": ["/home/<USER>/.arduino15/packages/esp32/tools/esp-x32/2411/bin/xtensa-esp32s3-elf-g++", "-M<PERSON>", "-c", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/cpp_flags", "-w", "-<PERSON><PERSON>", "-Werror=return-type", "-DF_CPU=240000000L", "-DARDUINO=10607", "-DARDUINO_ESP32S3_DEV", "-DARDUINO_ARCH_ESP32", "-DARDUINO_BOARD=\"ESP32S3_DEV\"", "-DARDUINO_VARIANT=\"esp32s3\"", "-DARDUINO_PARTITION_default", "-DARDUINO_HOST_OS=\"linux\"", "-DARDUINO_FQBN=\"esp32:esp32:esp32s3:UploadSpeed=921600,USBMode=hwcdc,CDCOnBoot=default,MSCOnBoot=default,DFUOnBoot=default,UploadMode=default,CPUFreq=240,FlashMode=qio,FlashSize=4M,PartitionScheme=default,DebugLevel=none,PSRAM=disabled,LoopCore=1,EventsCore=1,EraseFlash=none,JTAGAdapter=default,ZigbeeMode=default\"", "-DESP32=ESP32", "-DCORE_DEBUG_LEVEL=0", "-DARDUINO_RUNNING_CORE=1", "-DARDUINO_EVENT_RUNNING_CORE=1", "-DARDUINO_USB_MODE=1", "-DARDUINO_USB_CDC_ON_BOOT=0", "-DARDUINO_USB_MSC_ON_BOOT=0", "-DARDUINO_USB_DFU_ON_BOOT=0", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/defines", "-I/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta", "-iprefix", "/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/includes", "-I/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/qio_qspi/include", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/variants/esp32s3", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Wire/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/SPI/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WiFi/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Network/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WebServer/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/FS/src", "-I/home/<USER>/Documents/Arduino/libraries/NTPClient", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/SPIFFS/src", "-I/home/<USER>/Documents/Arduino/libraries/ArduinoJson/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/DNSServer/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/AsyncUDP/src", "-I/home/<USER>/Documents/Arduino/libraries/Adafruit_GFX_Library", "-I/home/<USER>/Documents/Arduino/libraries/Adafruit_BusIO", "-I/home/<USER>/Documents/Arduino/libraries/ESP32_HUB75_LED_MATRIX_PANEL_DMA_Display/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/HTTPClient/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/NetworkClientSecure/src", "-I/home/<USER>/Documents/Arduino/libraries/UnixTime/src", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/build_opt.h", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/file_opts", "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/NetworkClientSecure/src/ssl_client.cpp", "-o", "/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/libraries/NetworkClientSecure/ssl_client.cpp.o"], "file": "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/NetworkClientSecure/src/ssl_client.cpp"}, {"directory": "/home/<USER>", "arguments": ["/home/<USER>/.arduino15/packages/esp32/tools/esp-x32/2411/bin/xtensa-esp32s3-elf-g++", "-M<PERSON>", "-c", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/cpp_flags", "-w", "-<PERSON><PERSON>", "-Werror=return-type", "-DF_CPU=240000000L", "-DARDUINO=10607", "-DARDUINO_ESP32S3_DEV", "-DARDUINO_ARCH_ESP32", "-DARDUINO_BOARD=\"ESP32S3_DEV\"", "-DARDUINO_VARIANT=\"esp32s3\"", "-DARDUINO_PARTITION_default", "-DARDUINO_HOST_OS=\"linux\"", "-DARDUINO_FQBN=\"esp32:esp32:esp32s3:UploadSpeed=921600,USBMode=hwcdc,CDCOnBoot=default,MSCOnBoot=default,DFUOnBoot=default,UploadMode=default,CPUFreq=240,FlashMode=qio,FlashSize=4M,PartitionScheme=default,DebugLevel=none,PSRAM=disabled,LoopCore=1,EventsCore=1,EraseFlash=none,JTAGAdapter=default,ZigbeeMode=default\"", "-DESP32=ESP32", "-DCORE_DEBUG_LEVEL=0", "-DARDUINO_RUNNING_CORE=1", "-DARDUINO_EVENT_RUNNING_CORE=1", "-DARDUINO_USB_MODE=1", "-DARDUINO_USB_CDC_ON_BOOT=0", "-DARDUINO_USB_MSC_ON_BOOT=0", "-DARDUINO_USB_DFU_ON_BOOT=0", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/defines", "-I/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta", "-iprefix", "/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/includes", "-I/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/qio_qspi/include", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/variants/esp32s3", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Wire/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/SPI/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WiFi/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Network/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WebServer/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/FS/src", "-I/home/<USER>/Documents/Arduino/libraries/NTPClient", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/SPIFFS/src", "-I/home/<USER>/Documents/Arduino/libraries/ArduinoJson/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/DNSServer/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/AsyncUDP/src", "-I/home/<USER>/Documents/Arduino/libraries/Adafruit_GFX_Library", "-I/home/<USER>/Documents/Arduino/libraries/Adafruit_BusIO", "-I/home/<USER>/Documents/Arduino/libraries/ESP32_HUB75_LED_MATRIX_PANEL_DMA_Display/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/HTTPClient/src", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/NetworkClientSecure/src", "-I/home/<USER>/Documents/Arduino/libraries/UnixTime/src", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/build_opt.h", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/file_opts", "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/NetworkClientSecure/src/NetworkClientSecure.cpp", "-o", "/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/libraries/NetworkClientSecure/NetworkClientSecure.cpp.o"], "file": "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/NetworkClientSecure/src/NetworkClientSecure.cpp"}, {"directory": "/home/<USER>", "arguments": ["/home/<USER>/.arduino15/packages/esp32/tools/esp-x32/2411/bin/xtensa-esp32s3-elf-gcc", "-M<PERSON>", "-c", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/c_flags", "-w", "-<PERSON><PERSON>", "-Werror=return-type", "-DF_CPU=240000000L", "-DARDUINO=10607", "-DARDUINO_ESP32S3_DEV", "-DARDUINO_ARCH_ESP32", "-DARDUINO_BOARD=\"ESP32S3_DEV\"", "-DARDUINO_VARIANT=\"esp32s3\"", "-DARDUINO_PARTITION_default", "-DARDUINO_HOST_OS=\"linux\"", "-DARDUINO_FQBN=\"esp32:esp32:esp32s3:UploadSpeed=921600,USBMode=hwcdc,CDCOnBoot=default,MSCOnBoot=default,DFUOnBoot=default,UploadMode=default,CPUFreq=240,FlashMode=qio,FlashSize=4M,PartitionScheme=default,DebugLevel=none,PSRAM=disabled,LoopCore=1,EventsCore=1,EraseFlash=none,JTAGAdapter=default,ZigbeeMode=default\"", "-DESP32=ESP32", "-DCORE_DEBUG_LEVEL=0", "-DARDUINO_RUNNING_CORE=1", "-DARDUINO_EVENT_RUNNING_CORE=1", "-DARDUINO_USB_MODE=1", "-DARDUINO_USB_CDC_ON_BOOT=0", "-DARDUINO_USB_MSC_ON_BOOT=0", "-DARDUINO_USB_DFU_ON_BOOT=0", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/defines", "-I/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta", "-iprefix", "/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/includes", "-I/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/qio_qspi/include", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/variants/esp32s3", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/build_opt.h", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/file_opts", "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/ColorFormat.c", "-o", "/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/core/ColorFormat.c.o"], "file": "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/ColorFormat.c"}, {"directory": "/home/<USER>", "arguments": ["/home/<USER>/.arduino15/packages/esp32/tools/esp-x32/2411/bin/xtensa-esp32s3-elf-g++", "-M<PERSON>", "-c", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/cpp_flags", "-w", "-<PERSON><PERSON>", "-Werror=return-type", "-DF_CPU=240000000L", "-DARDUINO=10607", "-DARDUINO_ESP32S3_DEV", "-DARDUINO_ARCH_ESP32", "-DARDUINO_BOARD=\"ESP32S3_DEV\"", "-DARDUINO_VARIANT=\"esp32s3\"", "-DARDUINO_PARTITION_default", "-DARDUINO_HOST_OS=\"linux\"", "-DARDUINO_FQBN=\"esp32:esp32:esp32s3:UploadSpeed=921600,USBMode=hwcdc,CDCOnBoot=default,MSCOnBoot=default,DFUOnBoot=default,UploadMode=default,CPUFreq=240,FlashMode=qio,FlashSize=4M,PartitionScheme=default,DebugLevel=none,PSRAM=disabled,LoopCore=1,EventsCore=1,EraseFlash=none,JTAGAdapter=default,ZigbeeMode=default\"", "-DESP32=ESP32", "-DCORE_DEBUG_LEVEL=0", "-DARDUINO_RUNNING_CORE=1", "-DARDUINO_EVENT_RUNNING_CORE=1", "-DARDUINO_USB_MODE=1", "-DARDUINO_USB_CDC_ON_BOOT=0", "-DARDUINO_USB_MSC_ON_BOOT=0", "-DARDUINO_USB_DFU_ON_BOOT=0", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/defines", "-I/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta", "-iprefix", "/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/includes", "-I/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/qio_qspi/include", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/variants/esp32s3", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/build_opt.h", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/file_opts", "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/HEXBuilder.cpp", "-o", "/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/core/HEXBuilder.cpp.o"], "file": "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/HEXBuilder.cpp"}, {"directory": "/home/<USER>", "arguments": ["/home/<USER>/.arduino15/packages/esp32/tools/esp-x32/2411/bin/xtensa-esp32s3-elf-g++", "-M<PERSON>", "-c", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/cpp_flags", "-w", "-<PERSON><PERSON>", "-Werror=return-type", "-DF_CPU=240000000L", "-DARDUINO=10607", "-DARDUINO_ESP32S3_DEV", "-DARDUINO_ARCH_ESP32", "-DARDUINO_BOARD=\"ESP32S3_DEV\"", "-DARDUINO_VARIANT=\"esp32s3\"", "-DARDUINO_PARTITION_default", "-DARDUINO_HOST_OS=\"linux\"", "-DARDUINO_FQBN=\"esp32:esp32:esp32s3:UploadSpeed=921600,USBMode=hwcdc,CDCOnBoot=default,MSCOnBoot=default,DFUOnBoot=default,UploadMode=default,CPUFreq=240,FlashMode=qio,FlashSize=4M,PartitionScheme=default,DebugLevel=none,PSRAM=disabled,LoopCore=1,EventsCore=1,EraseFlash=none,JTAGAdapter=default,ZigbeeMode=default\"", "-DESP32=ESP32", "-DCORE_DEBUG_LEVEL=0", "-DARDUINO_RUNNING_CORE=1", "-DARDUINO_EVENT_RUNNING_CORE=1", "-DARDUINO_USB_MODE=1", "-DARDUINO_USB_CDC_ON_BOOT=0", "-DARDUINO_USB_MSC_ON_BOOT=0", "-DARDUINO_USB_DFU_ON_BOOT=0", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/defines", "-I/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta", "-iprefix", "/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/includes", "-I/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/qio_qspi/include", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/variants/esp32s3", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/build_opt.h", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/file_opts", "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/IPAddress.cpp", "-o", "/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/core/IPAddress.cpp.o"], "file": "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/IPAddress.cpp"}, {"directory": "/home/<USER>", "arguments": ["/home/<USER>/.arduino15/packages/esp32/tools/esp-x32/2411/bin/xtensa-esp32s3-elf-g++", "-M<PERSON>", "-c", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/cpp_flags", "-w", "-<PERSON><PERSON>", "-Werror=return-type", "-DF_CPU=240000000L", "-DARDUINO=10607", "-DARDUINO_ESP32S3_DEV", "-DARDUINO_ARCH_ESP32", "-DARDUINO_BOARD=\"ESP32S3_DEV\"", "-DARDUINO_VARIANT=\"esp32s3\"", "-DARDUINO_PARTITION_default", "-DARDUINO_HOST_OS=\"linux\"", "-DARDUINO_FQBN=\"esp32:esp32:esp32s3:UploadSpeed=921600,USBMode=hwcdc,CDCOnBoot=default,MSCOnBoot=default,DFUOnBoot=default,UploadMode=default,CPUFreq=240,FlashMode=qio,FlashSize=4M,PartitionScheme=default,DebugLevel=none,PSRAM=disabled,LoopCore=1,EventsCore=1,EraseFlash=none,JTAGAdapter=default,ZigbeeMode=default\"", "-DESP32=ESP32", "-DCORE_DEBUG_LEVEL=0", "-DARDUINO_RUNNING_CORE=1", "-DARDUINO_EVENT_RUNNING_CORE=1", "-DARDUINO_USB_MODE=1", "-DARDUINO_USB_CDC_ON_BOOT=0", "-DARDUINO_USB_MSC_ON_BOOT=0", "-DARDUINO_USB_DFU_ON_BOOT=0", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/defines", "-I/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta", "-iprefix", "/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/includes", "-I/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/qio_qspi/include", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/variants/esp32s3", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/build_opt.h", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/file_opts", "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/FirmwareMSC.cpp", "-o", "/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/core/FirmwareMSC.cpp.o"], "file": "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/FirmwareMSC.cpp"}, {"directory": "/home/<USER>", "arguments": ["/home/<USER>/.arduino15/packages/esp32/tools/esp-x32/2411/bin/xtensa-esp32s3-elf-g++", "-M<PERSON>", "-c", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/cpp_flags", "-w", "-<PERSON><PERSON>", "-Werror=return-type", "-DF_CPU=240000000L", "-DARDUINO=10607", "-DARDUINO_ESP32S3_DEV", "-DARDUINO_ARCH_ESP32", "-DARDUINO_BOARD=\"ESP32S3_DEV\"", "-DARDUINO_VARIANT=\"esp32s3\"", "-DARDUINO_PARTITION_default", "-DARDUINO_HOST_OS=\"linux\"", "-DARDUINO_FQBN=\"esp32:esp32:esp32s3:UploadSpeed=921600,USBMode=hwcdc,CDCOnBoot=default,MSCOnBoot=default,DFUOnBoot=default,UploadMode=default,CPUFreq=240,FlashMode=qio,FlashSize=4M,PartitionScheme=default,DebugLevel=none,PSRAM=disabled,LoopCore=1,EventsCore=1,EraseFlash=none,JTAGAdapter=default,ZigbeeMode=default\"", "-DESP32=ESP32", "-DCORE_DEBUG_LEVEL=0", "-DARDUINO_RUNNING_CORE=1", "-DARDUINO_EVENT_RUNNING_CORE=1", "-DARDUINO_USB_MODE=1", "-DARDUINO_USB_CDC_ON_BOOT=0", "-DARDUINO_USB_MSC_ON_BOOT=0", "-DARDUINO_USB_DFU_ON_BOOT=0", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/defines", "-I/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta", "-iprefix", "/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/includes", "-I/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/qio_qspi/include", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/variants/esp32s3", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/build_opt.h", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/file_opts", "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/Esp.cpp", "-o", "/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/core/Esp.cpp.o"], "file": "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/Esp.cpp"}, {"directory": "/home/<USER>", "arguments": ["/home/<USER>/.arduino15/packages/esp32/tools/esp-x32/2411/bin/xtensa-esp32s3-elf-g++", "-M<PERSON>", "-c", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/cpp_flags", "-w", "-<PERSON><PERSON>", "-Werror=return-type", "-DF_CPU=240000000L", "-DARDUINO=10607", "-DARDUINO_ESP32S3_DEV", "-DARDUINO_ARCH_ESP32", "-DARDUINO_BOARD=\"ESP32S3_DEV\"", "-DARDUINO_VARIANT=\"esp32s3\"", "-DARDUINO_PARTITION_default", "-DARDUINO_HOST_OS=\"linux\"", "-DARDUINO_FQBN=\"esp32:esp32:esp32s3:UploadSpeed=921600,USBMode=hwcdc,CDCOnBoot=default,MSCOnBoot=default,DFUOnBoot=default,UploadMode=default,CPUFreq=240,FlashMode=qio,FlashSize=4M,PartitionScheme=default,DebugLevel=none,PSRAM=disabled,LoopCore=1,EventsCore=1,EraseFlash=none,JTAGAdapter=default,ZigbeeMode=default\"", "-DESP32=ESP32", "-DCORE_DEBUG_LEVEL=0", "-DARDUINO_RUNNING_CORE=1", "-DARDUINO_EVENT_RUNNING_CORE=1", "-DARDUINO_USB_MODE=1", "-DARDUINO_USB_CDC_ON_BOOT=0", "-DARDUINO_USB_MSC_ON_BOOT=0", "-DARDUINO_USB_DFU_ON_BOOT=0", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/defines", "-I/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta", "-iprefix", "/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/includes", "-I/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/qio_qspi/include", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/variants/esp32s3", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/build_opt.h", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/file_opts", "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/HWCDC.cpp", "-o", "/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/core/HWCDC.cpp.o"], "file": "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/HWCDC.cpp"}, {"directory": "/home/<USER>", "arguments": ["/home/<USER>/.arduino15/packages/esp32/tools/esp-x32/2411/bin/xtensa-esp32s3-elf-g++", "-M<PERSON>", "-c", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/cpp_flags", "-w", "-<PERSON><PERSON>", "-Werror=return-type", "-DF_CPU=240000000L", "-DARDUINO=10607", "-DARDUINO_ESP32S3_DEV", "-DARDUINO_ARCH_ESP32", "-DARDUINO_BOARD=\"ESP32S3_DEV\"", "-DARDUINO_VARIANT=\"esp32s3\"", "-DARDUINO_PARTITION_default", "-DARDUINO_HOST_OS=\"linux\"", "-DARDUINO_FQBN=\"esp32:esp32:esp32s3:UploadSpeed=921600,USBMode=hwcdc,CDCOnBoot=default,MSCOnBoot=default,DFUOnBoot=default,UploadMode=default,CPUFreq=240,FlashMode=qio,FlashSize=4M,PartitionScheme=default,DebugLevel=none,PSRAM=disabled,LoopCore=1,EventsCore=1,EraseFlash=none,JTAGAdapter=default,ZigbeeMode=default\"", "-DESP32=ESP32", "-DCORE_DEBUG_LEVEL=0", "-DARDUINO_RUNNING_CORE=1", "-DARDUINO_EVENT_RUNNING_CORE=1", "-DARDUINO_USB_MODE=1", "-DARDUINO_USB_CDC_ON_BOOT=0", "-DARDUINO_USB_MSC_ON_BOOT=0", "-DARDUINO_USB_DFU_ON_BOOT=0", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/defines", "-I/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta", "-iprefix", "/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/includes", "-I/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/qio_qspi/include", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/variants/esp32s3", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/build_opt.h", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/file_opts", "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/FunctionalInterrupt.cpp", "-o", "/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/core/FunctionalInterrupt.cpp.o"], "file": "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/FunctionalInterrupt.cpp"}, {"directory": "/home/<USER>", "arguments": ["/home/<USER>/.arduino15/packages/esp32/tools/esp-x32/2411/bin/xtensa-esp32s3-elf-g++", "-M<PERSON>", "-c", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/cpp_flags", "-w", "-<PERSON><PERSON>", "-Werror=return-type", "-DF_CPU=240000000L", "-DARDUINO=10607", "-DARDUINO_ESP32S3_DEV", "-DARDUINO_ARCH_ESP32", "-DARDUINO_BOARD=\"ESP32S3_DEV\"", "-DARDUINO_VARIANT=\"esp32s3\"", "-DARDUINO_PARTITION_default", "-DARDUINO_HOST_OS=\"linux\"", "-DARDUINO_FQBN=\"esp32:esp32:esp32s3:UploadSpeed=921600,USBMode=hwcdc,CDCOnBoot=default,MSCOnBoot=default,DFUOnBoot=default,UploadMode=default,CPUFreq=240,FlashMode=qio,FlashSize=4M,PartitionScheme=default,DebugLevel=none,PSRAM=disabled,LoopCore=1,EventsCore=1,EraseFlash=none,JTAGAdapter=default,ZigbeeMode=default\"", "-DESP32=ESP32", "-DCORE_DEBUG_LEVEL=0", "-DARDUINO_RUNNING_CORE=1", "-DARDUINO_EVENT_RUNNING_CORE=1", "-DARDUINO_USB_MODE=1", "-DARDUINO_USB_CDC_ON_BOOT=0", "-DARDUINO_USB_MSC_ON_BOOT=0", "-DARDUINO_USB_DFU_ON_BOOT=0", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/defines", "-I/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta", "-iprefix", "/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/includes", "-I/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/qio_qspi/include", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/variants/esp32s3", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/build_opt.h", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/file_opts", "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/HardwareSerial.cpp", "-o", "/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/core/HardwareSerial.cpp.o"], "file": "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/HardwareSerial.cpp"}, {"directory": "/home/<USER>", "arguments": ["/home/<USER>/.arduino15/packages/esp32/tools/esp-x32/2411/bin/xtensa-esp32s3-elf-g++", "-M<PERSON>", "-c", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/cpp_flags", "-w", "-<PERSON><PERSON>", "-Werror=return-type", "-DF_CPU=240000000L", "-DARDUINO=10607", "-DARDUINO_ESP32S3_DEV", "-DARDUINO_ARCH_ESP32", "-DARDUINO_BOARD=\"ESP32S3_DEV\"", "-DARDUINO_VARIANT=\"esp32s3\"", "-DARDUINO_PARTITION_default", "-DARDUINO_HOST_OS=\"linux\"", "-DARDUINO_FQBN=\"esp32:esp32:esp32s3:UploadSpeed=921600,USBMode=hwcdc,CDCOnBoot=default,MSCOnBoot=default,DFUOnBoot=default,UploadMode=default,CPUFreq=240,FlashMode=qio,FlashSize=4M,PartitionScheme=default,DebugLevel=none,PSRAM=disabled,LoopCore=1,EventsCore=1,EraseFlash=none,JTAGAdapter=default,ZigbeeMode=default\"", "-DESP32=ESP32", "-DCORE_DEBUG_LEVEL=0", "-DARDUINO_RUNNING_CORE=1", "-DARDUINO_EVENT_RUNNING_CORE=1", "-DARDUINO_USB_MODE=1", "-DARDUINO_USB_CDC_ON_BOOT=0", "-DARDUINO_USB_MSC_ON_BOOT=0", "-DARDUINO_USB_DFU_ON_BOOT=0", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/defines", "-I/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta", "-iprefix", "/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/includes", "-I/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/qio_qspi/include", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/variants/esp32s3", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/build_opt.h", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/file_opts", "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/MD5Builder.cpp", "-o", "/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/core/MD5Builder.cpp.o"], "file": "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/MD5Builder.cpp"}, {"directory": "/home/<USER>", "arguments": ["/home/<USER>/.arduino15/packages/esp32/tools/esp-x32/2411/bin/xtensa-esp32s3-elf-g++", "-M<PERSON>", "-c", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/cpp_flags", "-w", "-<PERSON><PERSON>", "-Werror=return-type", "-DF_CPU=240000000L", "-DARDUINO=10607", "-DARDUINO_ESP32S3_DEV", "-DARDUINO_ARCH_ESP32", "-DARDUINO_BOARD=\"ESP32S3_DEV\"", "-DARDUINO_VARIANT=\"esp32s3\"", "-DARDUINO_PARTITION_default", "-DARDUINO_HOST_OS=\"linux\"", "-DARDUINO_FQBN=\"esp32:esp32:esp32s3:UploadSpeed=921600,USBMode=hwcdc,CDCOnBoot=default,MSCOnBoot=default,DFUOnBoot=default,UploadMode=default,CPUFreq=240,FlashMode=qio,FlashSize=4M,PartitionScheme=default,DebugLevel=none,PSRAM=disabled,LoopCore=1,EventsCore=1,EraseFlash=none,JTAGAdapter=default,ZigbeeMode=default\"", "-DESP32=ESP32", "-DCORE_DEBUG_LEVEL=0", "-DARDUINO_RUNNING_CORE=1", "-DARDUINO_EVENT_RUNNING_CORE=1", "-DARDUINO_USB_MODE=1", "-DARDUINO_USB_CDC_ON_BOOT=0", "-DARDUINO_USB_MSC_ON_BOOT=0", "-DARDUINO_USB_DFU_ON_BOOT=0", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/defines", "-I/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta", "-iprefix", "/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/includes", "-I/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/qio_qspi/include", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/variants/esp32s3", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/build_opt.h", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/file_opts", "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/MacAddress.cpp", "-o", "/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/core/MacAddress.cpp.o"], "file": "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/MacAddress.cpp"}, {"directory": "/home/<USER>", "arguments": ["/home/<USER>/.arduino15/packages/esp32/tools/esp-x32/2411/bin/xtensa-esp32s3-elf-g++", "-M<PERSON>", "-c", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/cpp_flags", "-w", "-<PERSON><PERSON>", "-Werror=return-type", "-DF_CPU=240000000L", "-DARDUINO=10607", "-DARDUINO_ESP32S3_DEV", "-DARDUINO_ARCH_ESP32", "-DARDUINO_BOARD=\"ESP32S3_DEV\"", "-DARDUINO_VARIANT=\"esp32s3\"", "-DARDUINO_PARTITION_default", "-DARDUINO_HOST_OS=\"linux\"", "-DARDUINO_FQBN=\"esp32:esp32:esp32s3:UploadSpeed=921600,USBMode=hwcdc,CDCOnBoot=default,MSCOnBoot=default,DFUOnBoot=default,UploadMode=default,CPUFreq=240,FlashMode=qio,FlashSize=4M,PartitionScheme=default,DebugLevel=none,PSRAM=disabled,LoopCore=1,EventsCore=1,EraseFlash=none,JTAGAdapter=default,ZigbeeMode=default\"", "-DESP32=ESP32", "-DCORE_DEBUG_LEVEL=0", "-DARDUINO_RUNNING_CORE=1", "-DARDUINO_EVENT_RUNNING_CORE=1", "-DARDUINO_USB_MODE=1", "-DARDUINO_USB_CDC_ON_BOOT=0", "-DARDUINO_USB_MSC_ON_BOOT=0", "-DARDUINO_USB_DFU_ON_BOOT=0", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/defines", "-I/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta", "-iprefix", "/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/includes", "-I/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/qio_qspi/include", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/variants/esp32s3", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/build_opt.h", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/file_opts", "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/Print.cpp", "-o", "/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/core/Print.cpp.o"], "file": "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/Print.cpp"}, {"directory": "/home/<USER>", "arguments": ["/home/<USER>/.arduino15/packages/esp32/tools/esp-x32/2411/bin/xtensa-esp32s3-elf-g++", "-M<PERSON>", "-c", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/cpp_flags", "-w", "-<PERSON><PERSON>", "-Werror=return-type", "-DF_CPU=240000000L", "-DARDUINO=10607", "-DARDUINO_ESP32S3_DEV", "-DARDUINO_ARCH_ESP32", "-DARDUINO_BOARD=\"ESP32S3_DEV\"", "-DARDUINO_VARIANT=\"esp32s3\"", "-DARDUINO_PARTITION_default", "-DARDUINO_HOST_OS=\"linux\"", "-DARDUINO_FQBN=\"esp32:esp32:esp32s3:UploadSpeed=921600,USBMode=hwcdc,CDCOnBoot=default,MSCOnBoot=default,DFUOnBoot=default,UploadMode=default,CPUFreq=240,FlashMode=qio,FlashSize=4M,PartitionScheme=default,DebugLevel=none,PSRAM=disabled,LoopCore=1,EventsCore=1,EraseFlash=none,JTAGAdapter=default,ZigbeeMode=default\"", "-DESP32=ESP32", "-DCORE_DEBUG_LEVEL=0", "-DARDUINO_RUNNING_CORE=1", "-DARDUINO_EVENT_RUNNING_CORE=1", "-DARDUINO_USB_MODE=1", "-DARDUINO_USB_CDC_ON_BOOT=0", "-DARDUINO_USB_MSC_ON_BOOT=0", "-DARDUINO_USB_DFU_ON_BOOT=0", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/defines", "-I/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta", "-iprefix", "/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/includes", "-I/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/qio_qspi/include", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/variants/esp32s3", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/build_opt.h", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/file_opts", "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/SHA1Builder.cpp", "-o", "/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/core/SHA1Builder.cpp.o"], "file": "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/SHA1Builder.cpp"}, {"directory": "/home/<USER>", "arguments": ["/home/<USER>/.arduino15/packages/esp32/tools/esp-x32/2411/bin/xtensa-esp32s3-elf-g++", "-M<PERSON>", "-c", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/cpp_flags", "-w", "-<PERSON><PERSON>", "-Werror=return-type", "-DF_CPU=240000000L", "-DARDUINO=10607", "-DARDUINO_ESP32S3_DEV", "-DARDUINO_ARCH_ESP32", "-DARDUINO_BOARD=\"ESP32S3_DEV\"", "-DARDUINO_VARIANT=\"esp32s3\"", "-DARDUINO_PARTITION_default", "-DARDUINO_HOST_OS=\"linux\"", "-DARDUINO_FQBN=\"esp32:esp32:esp32s3:UploadSpeed=921600,USBMode=hwcdc,CDCOnBoot=default,MSCOnBoot=default,DFUOnBoot=default,UploadMode=default,CPUFreq=240,FlashMode=qio,FlashSize=4M,PartitionScheme=default,DebugLevel=none,PSRAM=disabled,LoopCore=1,EventsCore=1,EraseFlash=none,JTAGAdapter=default,ZigbeeMode=default\"", "-DESP32=ESP32", "-DCORE_DEBUG_LEVEL=0", "-DARDUINO_RUNNING_CORE=1", "-DARDUINO_EVENT_RUNNING_CORE=1", "-DARDUINO_USB_MODE=1", "-DARDUINO_USB_CDC_ON_BOOT=0", "-DARDUINO_USB_MSC_ON_BOOT=0", "-DARDUINO_USB_DFU_ON_BOOT=0", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/defines", "-I/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta", "-iprefix", "/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/includes", "-I/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/qio_qspi/include", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/variants/esp32s3", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/build_opt.h", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/file_opts", "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/Stream.cpp", "-o", "/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/core/Stream.cpp.o"], "file": "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/Stream.cpp"}, {"directory": "/home/<USER>", "arguments": ["/home/<USER>/.arduino15/packages/esp32/tools/esp-x32/2411/bin/xtensa-esp32s3-elf-g++", "-M<PERSON>", "-c", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/cpp_flags", "-w", "-<PERSON><PERSON>", "-Werror=return-type", "-DF_CPU=240000000L", "-DARDUINO=10607", "-DARDUINO_ESP32S3_DEV", "-DARDUINO_ARCH_ESP32", "-DARDUINO_BOARD=\"ESP32S3_DEV\"", "-DARDUINO_VARIANT=\"esp32s3\"", "-DARDUINO_PARTITION_default", "-DARDUINO_HOST_OS=\"linux\"", "-DARDUINO_FQBN=\"esp32:esp32:esp32s3:UploadSpeed=921600,USBMode=hwcdc,CDCOnBoot=default,MSCOnBoot=default,DFUOnBoot=default,UploadMode=default,CPUFreq=240,FlashMode=qio,FlashSize=4M,PartitionScheme=default,DebugLevel=none,PSRAM=disabled,LoopCore=1,EventsCore=1,EraseFlash=none,JTAGAdapter=default,ZigbeeMode=default\"", "-DESP32=ESP32", "-DCORE_DEBUG_LEVEL=0", "-DARDUINO_RUNNING_CORE=1", "-DARDUINO_EVENT_RUNNING_CORE=1", "-DARDUINO_USB_MODE=1", "-DARDUINO_USB_CDC_ON_BOOT=0", "-DARDUINO_USB_MSC_ON_BOOT=0", "-DARDUINO_USB_DFU_ON_BOOT=0", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/defines", "-I/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta", "-iprefix", "/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/includes", "-I/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/qio_qspi/include", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/variants/esp32s3", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/build_opt.h", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/file_opts", "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/StreamString.cpp", "-o", "/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/core/StreamString.cpp.o"], "file": "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/StreamString.cpp"}, {"directory": "/home/<USER>", "arguments": ["/home/<USER>/.arduino15/packages/esp32/tools/esp-x32/2411/bin/xtensa-esp32s3-elf-g++", "-M<PERSON>", "-c", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/cpp_flags", "-w", "-<PERSON><PERSON>", "-Werror=return-type", "-DF_CPU=240000000L", "-DARDUINO=10607", "-DARDUINO_ESP32S3_DEV", "-DARDUINO_ARCH_ESP32", "-DARDUINO_BOARD=\"ESP32S3_DEV\"", "-DARDUINO_VARIANT=\"esp32s3\"", "-DARDUINO_PARTITION_default", "-DARDUINO_HOST_OS=\"linux\"", "-DARDUINO_FQBN=\"esp32:esp32:esp32s3:UploadSpeed=921600,USBMode=hwcdc,CDCOnBoot=default,MSCOnBoot=default,DFUOnBoot=default,UploadMode=default,CPUFreq=240,FlashMode=qio,FlashSize=4M,PartitionScheme=default,DebugLevel=none,PSRAM=disabled,LoopCore=1,EventsCore=1,EraseFlash=none,JTAGAdapter=default,ZigbeeMode=default\"", "-DESP32=ESP32", "-DCORE_DEBUG_LEVEL=0", "-DARDUINO_RUNNING_CORE=1", "-DARDUINO_EVENT_RUNNING_CORE=1", "-DARDUINO_USB_MODE=1", "-DARDUINO_USB_CDC_ON_BOOT=0", "-DARDUINO_USB_MSC_ON_BOOT=0", "-DARDUINO_USB_DFU_ON_BOOT=0", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/defines", "-I/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta", "-iprefix", "/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/includes", "-I/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/qio_qspi/include", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/variants/esp32s3", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/build_opt.h", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/file_opts", "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/Tone.cpp", "-o", "/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/core/Tone.cpp.o"], "file": "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/Tone.cpp"}, {"directory": "/home/<USER>", "arguments": ["/home/<USER>/.arduino15/packages/esp32/tools/esp-x32/2411/bin/xtensa-esp32s3-elf-g++", "-M<PERSON>", "-c", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/cpp_flags", "-w", "-<PERSON><PERSON>", "-Werror=return-type", "-DF_CPU=240000000L", "-DARDUINO=10607", "-DARDUINO_ESP32S3_DEV", "-DARDUINO_ARCH_ESP32", "-DARDUINO_BOARD=\"ESP32S3_DEV\"", "-DARDUINO_VARIANT=\"esp32s3\"", "-DARDUINO_PARTITION_default", "-DARDUINO_HOST_OS=\"linux\"", "-DARDUINO_FQBN=\"esp32:esp32:esp32s3:UploadSpeed=921600,USBMode=hwcdc,CDCOnBoot=default,MSCOnBoot=default,DFUOnBoot=default,UploadMode=default,CPUFreq=240,FlashMode=qio,FlashSize=4M,PartitionScheme=default,DebugLevel=none,PSRAM=disabled,LoopCore=1,EventsCore=1,EraseFlash=none,JTAGAdapter=default,ZigbeeMode=default\"", "-DESP32=ESP32", "-DCORE_DEBUG_LEVEL=0", "-DARDUINO_RUNNING_CORE=1", "-DARDUINO_EVENT_RUNNING_CORE=1", "-DARDUINO_USB_MODE=1", "-DARDUINO_USB_CDC_ON_BOOT=0", "-DARDUINO_USB_MSC_ON_BOOT=0", "-DARDUINO_USB_DFU_ON_BOOT=0", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/defines", "-I/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta", "-iprefix", "/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/includes", "-I/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/qio_qspi/include", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/variants/esp32s3", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/build_opt.h", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/file_opts", "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/USB.cpp", "-o", "/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/core/USB.cpp.o"], "file": "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/USB.cpp"}, {"directory": "/home/<USER>", "arguments": ["/home/<USER>/.arduino15/packages/esp32/tools/esp-x32/2411/bin/xtensa-esp32s3-elf-g++", "-M<PERSON>", "-c", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/cpp_flags", "-w", "-<PERSON><PERSON>", "-Werror=return-type", "-DF_CPU=240000000L", "-DARDUINO=10607", "-DARDUINO_ESP32S3_DEV", "-DARDUINO_ARCH_ESP32", "-DARDUINO_BOARD=\"ESP32S3_DEV\"", "-DARDUINO_VARIANT=\"esp32s3\"", "-DARDUINO_PARTITION_default", "-DARDUINO_HOST_OS=\"linux\"", "-DARDUINO_FQBN=\"esp32:esp32:esp32s3:UploadSpeed=921600,USBMode=hwcdc,CDCOnBoot=default,MSCOnBoot=default,DFUOnBoot=default,UploadMode=default,CPUFreq=240,FlashMode=qio,FlashSize=4M,PartitionScheme=default,DebugLevel=none,PSRAM=disabled,LoopCore=1,EventsCore=1,EraseFlash=none,JTAGAdapter=default,ZigbeeMode=default\"", "-DESP32=ESP32", "-DCORE_DEBUG_LEVEL=0", "-DARDUINO_RUNNING_CORE=1", "-DARDUINO_EVENT_RUNNING_CORE=1", "-DARDUINO_USB_MODE=1", "-DARDUINO_USB_CDC_ON_BOOT=0", "-DARDUINO_USB_MSC_ON_BOOT=0", "-DARDUINO_USB_DFU_ON_BOOT=0", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/defines", "-I/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta", "-iprefix", "/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/includes", "-I/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/qio_qspi/include", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/variants/esp32s3", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/build_opt.h", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/file_opts", "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/USBCDC.cpp", "-o", "/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/core/USBCDC.cpp.o"], "file": "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/USBCDC.cpp"}, {"directory": "/home/<USER>", "arguments": ["/home/<USER>/.arduino15/packages/esp32/tools/esp-x32/2411/bin/xtensa-esp32s3-elf-g++", "-M<PERSON>", "-c", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/cpp_flags", "-w", "-<PERSON><PERSON>", "-Werror=return-type", "-DF_CPU=240000000L", "-DARDUINO=10607", "-DARDUINO_ESP32S3_DEV", "-DARDUINO_ARCH_ESP32", "-DARDUINO_BOARD=\"ESP32S3_DEV\"", "-DARDUINO_VARIANT=\"esp32s3\"", "-DARDUINO_PARTITION_default", "-DARDUINO_HOST_OS=\"linux\"", "-DARDUINO_FQBN=\"esp32:esp32:esp32s3:UploadSpeed=921600,USBMode=hwcdc,CDCOnBoot=default,MSCOnBoot=default,DFUOnBoot=default,UploadMode=default,CPUFreq=240,FlashMode=qio,FlashSize=4M,PartitionScheme=default,DebugLevel=none,PSRAM=disabled,LoopCore=1,EventsCore=1,EraseFlash=none,JTAGAdapter=default,ZigbeeMode=default\"", "-DESP32=ESP32", "-DCORE_DEBUG_LEVEL=0", "-DARDUINO_RUNNING_CORE=1", "-DARDUINO_EVENT_RUNNING_CORE=1", "-DARDUINO_USB_MODE=1", "-DARDUINO_USB_CDC_ON_BOOT=0", "-DARDUINO_USB_MSC_ON_BOOT=0", "-DARDUINO_USB_DFU_ON_BOOT=0", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/defines", "-I/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta", "-iprefix", "/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/includes", "-I/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/qio_qspi/include", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/variants/esp32s3", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/build_opt.h", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/file_opts", "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/USBMSC.cpp", "-o", "/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/core/USBMSC.cpp.o"], "file": "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/USBMSC.cpp"}, {"directory": "/home/<USER>", "arguments": ["/home/<USER>/.arduino15/packages/esp32/tools/esp-x32/2411/bin/xtensa-esp32s3-elf-g++", "-M<PERSON>", "-c", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/cpp_flags", "-w", "-<PERSON><PERSON>", "-Werror=return-type", "-DF_CPU=240000000L", "-DARDUINO=10607", "-DARDUINO_ESP32S3_DEV", "-DARDUINO_ARCH_ESP32", "-DARDUINO_BOARD=\"ESP32S3_DEV\"", "-DARDUINO_VARIANT=\"esp32s3\"", "-DARDUINO_PARTITION_default", "-DARDUINO_HOST_OS=\"linux\"", "-DARDUINO_FQBN=\"esp32:esp32:esp32s3:UploadSpeed=921600,USBMode=hwcdc,CDCOnBoot=default,MSCOnBoot=default,DFUOnBoot=default,UploadMode=default,CPUFreq=240,FlashMode=qio,FlashSize=4M,PartitionScheme=default,DebugLevel=none,PSRAM=disabled,LoopCore=1,EventsCore=1,EraseFlash=none,JTAGAdapter=default,ZigbeeMode=default\"", "-DESP32=ESP32", "-DCORE_DEBUG_LEVEL=0", "-DARDUINO_RUNNING_CORE=1", "-DARDUINO_EVENT_RUNNING_CORE=1", "-DARDUINO_USB_MODE=1", "-DARDUINO_USB_CDC_ON_BOOT=0", "-DARDUINO_USB_MSC_ON_BOOT=0", "-DARDUINO_USB_DFU_ON_BOOT=0", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/defines", "-I/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta", "-iprefix", "/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/includes", "-I/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/qio_qspi/include", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/variants/esp32s3", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/build_opt.h", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/file_opts", "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/WMath.cpp", "-o", "/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/core/WMath.cpp.o"], "file": "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/WMath.cpp"}, {"directory": "/home/<USER>", "arguments": ["/home/<USER>/.arduino15/packages/esp32/tools/esp-x32/2411/bin/xtensa-esp32s3-elf-g++", "-M<PERSON>", "-c", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/cpp_flags", "-w", "-<PERSON><PERSON>", "-Werror=return-type", "-DF_CPU=240000000L", "-DARDUINO=10607", "-DARDUINO_ESP32S3_DEV", "-DARDUINO_ARCH_ESP32", "-DARDUINO_BOARD=\"ESP32S3_DEV\"", "-DARDUINO_VARIANT=\"esp32s3\"", "-DARDUINO_PARTITION_default", "-DARDUINO_HOST_OS=\"linux\"", "-DARDUINO_FQBN=\"esp32:esp32:esp32s3:UploadSpeed=921600,USBMode=hwcdc,CDCOnBoot=default,MSCOnBoot=default,DFUOnBoot=default,UploadMode=default,CPUFreq=240,FlashMode=qio,FlashSize=4M,PartitionScheme=default,DebugLevel=none,PSRAM=disabled,LoopCore=1,EventsCore=1,EraseFlash=none,JTAGAdapter=default,ZigbeeMode=default\"", "-DESP32=ESP32", "-DCORE_DEBUG_LEVEL=0", "-DARDUINO_RUNNING_CORE=1", "-DARDUINO_EVENT_RUNNING_CORE=1", "-DARDUINO_USB_MODE=1", "-DARDUINO_USB_CDC_ON_BOOT=0", "-DARDUINO_USB_MSC_ON_BOOT=0", "-DARDUINO_USB_DFU_ON_BOOT=0", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/defines", "-I/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta", "-iprefix", "/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/includes", "-I/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/qio_qspi/include", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/variants/esp32s3", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/build_opt.h", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/file_opts", "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/WString.cpp", "-o", "/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/core/WString.cpp.o"], "file": "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/WString.cpp"}, {"directory": "/home/<USER>", "arguments": ["/home/<USER>/.arduino15/packages/esp32/tools/esp-x32/2411/bin/xtensa-esp32s3-elf-g++", "-M<PERSON>", "-c", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/cpp_flags", "-w", "-<PERSON><PERSON>", "-Werror=return-type", "-DF_CPU=240000000L", "-DARDUINO=10607", "-DARDUINO_ESP32S3_DEV", "-DARDUINO_ARCH_ESP32", "-DARDUINO_BOARD=\"ESP32S3_DEV\"", "-DARDUINO_VARIANT=\"esp32s3\"", "-DARDUINO_PARTITION_default", "-DARDUINO_HOST_OS=\"linux\"", "-DARDUINO_FQBN=\"esp32:esp32:esp32s3:UploadSpeed=921600,USBMode=hwcdc,CDCOnBoot=default,MSCOnBoot=default,DFUOnBoot=default,UploadMode=default,CPUFreq=240,FlashMode=qio,FlashSize=4M,PartitionScheme=default,DebugLevel=none,PSRAM=disabled,LoopCore=1,EventsCore=1,EraseFlash=none,JTAGAdapter=default,ZigbeeMode=default\"", "-DESP32=ESP32", "-DCORE_DEBUG_LEVEL=0", "-DARDUINO_RUNNING_CORE=1", "-DARDUINO_EVENT_RUNNING_CORE=1", "-DARDUINO_USB_MODE=1", "-DARDUINO_USB_CDC_ON_BOOT=0", "-DARDUINO_USB_MSC_ON_BOOT=0", "-DARDUINO_USB_DFU_ON_BOOT=0", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/defines", "-I/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta", "-iprefix", "/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/includes", "-I/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/qio_qspi/include", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/variants/esp32s3", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/build_opt.h", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/file_opts", "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/base64.cpp", "-o", "/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/core/base64.cpp.o"], "file": "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/base64.cpp"}, {"directory": "/home/<USER>", "arguments": ["/home/<USER>/.arduino15/packages/esp32/tools/esp-x32/2411/bin/xtensa-esp32s3-elf-g++", "-M<PERSON>", "-c", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/cpp_flags", "-w", "-<PERSON><PERSON>", "-Werror=return-type", "-DF_CPU=240000000L", "-DARDUINO=10607", "-DARDUINO_ESP32S3_DEV", "-DARDUINO_ARCH_ESP32", "-DARDUINO_BOARD=\"ESP32S3_DEV\"", "-DARDUINO_VARIANT=\"esp32s3\"", "-DARDUINO_PARTITION_default", "-DARDUINO_HOST_OS=\"linux\"", "-DARDUINO_FQBN=\"esp32:esp32:esp32s3:UploadSpeed=921600,USBMode=hwcdc,CDCOnBoot=default,MSCOnBoot=default,DFUOnBoot=default,UploadMode=default,CPUFreq=240,FlashMode=qio,FlashSize=4M,PartitionScheme=default,DebugLevel=none,PSRAM=disabled,LoopCore=1,EventsCore=1,EraseFlash=none,JTAGAdapter=default,ZigbeeMode=default\"", "-DESP32=ESP32", "-DCORE_DEBUG_LEVEL=0", "-DARDUINO_RUNNING_CORE=1", "-DARDUINO_EVENT_RUNNING_CORE=1", "-DARDUINO_USB_MODE=1", "-DARDUINO_USB_CDC_ON_BOOT=0", "-DARDUINO_USB_MSC_ON_BOOT=0", "-DARDUINO_USB_DFU_ON_BOOT=0", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/defines", "-I/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta", "-iprefix", "/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/includes", "-I/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/qio_qspi/include", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/variants/esp32s3", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/build_opt.h", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/file_opts", "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/cbuf.cpp", "-o", "/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/core/cbuf.cpp.o"], "file": "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/cbuf.cpp"}, {"directory": "/home/<USER>", "arguments": ["/home/<USER>/.arduino15/packages/esp32/tools/esp-x32/2411/bin/xtensa-esp32s3-elf-g++", "-M<PERSON>", "-c", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/cpp_flags", "-w", "-<PERSON><PERSON>", "-Werror=return-type", "-DF_CPU=240000000L", "-DARDUINO=10607", "-DARDUINO_ESP32S3_DEV", "-DARDUINO_ARCH_ESP32", "-DARDUINO_BOARD=\"ESP32S3_DEV\"", "-DARDUINO_VARIANT=\"esp32s3\"", "-DARDUINO_PARTITION_default", "-DARDUINO_HOST_OS=\"linux\"", "-DARDUINO_FQBN=\"esp32:esp32:esp32s3:UploadSpeed=921600,USBMode=hwcdc,CDCOnBoot=default,MSCOnBoot=default,DFUOnBoot=default,UploadMode=default,CPUFreq=240,FlashMode=qio,FlashSize=4M,PartitionScheme=default,DebugLevel=none,PSRAM=disabled,LoopCore=1,EventsCore=1,EraseFlash=none,JTAGAdapter=default,ZigbeeMode=default\"", "-DESP32=ESP32", "-DCORE_DEBUG_LEVEL=0", "-DARDUINO_RUNNING_CORE=1", "-DARDUINO_EVENT_RUNNING_CORE=1", "-DARDUINO_USB_MODE=1", "-DARDUINO_USB_CDC_ON_BOOT=0", "-DARDUINO_USB_MSC_ON_BOOT=0", "-DARDUINO_USB_DFU_ON_BOOT=0", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/defines", "-I/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta", "-iprefix", "/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/includes", "-I/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/qio_qspi/include", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/variants/esp32s3", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/build_opt.h", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/file_opts", "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/chip-debug-report.cpp", "-o", "/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/core/chip-debug-report.cpp.o"], "file": "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/chip-debug-report.cpp"}, {"directory": "/home/<USER>", "arguments": ["/home/<USER>/.arduino15/packages/esp32/tools/esp-x32/2411/bin/xtensa-esp32s3-elf-gcc", "-M<PERSON>", "-c", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/c_flags", "-w", "-<PERSON><PERSON>", "-Werror=return-type", "-DF_CPU=240000000L", "-DARDUINO=10607", "-DARDUINO_ESP32S3_DEV", "-DARDUINO_ARCH_ESP32", "-DARDUINO_BOARD=\"ESP32S3_DEV\"", "-DARDUINO_VARIANT=\"esp32s3\"", "-DARDUINO_PARTITION_default", "-DARDUINO_HOST_OS=\"linux\"", "-DARDUINO_FQBN=\"esp32:esp32:esp32s3:UploadSpeed=921600,USBMode=hwcdc,CDCOnBoot=default,MSCOnBoot=default,DFUOnBoot=default,UploadMode=default,CPUFreq=240,FlashMode=qio,FlashSize=4M,PartitionScheme=default,DebugLevel=none,PSRAM=disabled,LoopCore=1,EventsCore=1,EraseFlash=none,JTAGAdapter=default,ZigbeeMode=default\"", "-DESP32=ESP32", "-DCORE_DEBUG_LEVEL=0", "-DARDUINO_RUNNING_CORE=1", "-DARDUINO_EVENT_RUNNING_CORE=1", "-DARDUINO_USB_MODE=1", "-DARDUINO_USB_CDC_ON_BOOT=0", "-DARDUINO_USB_MSC_ON_BOOT=0", "-DARDUINO_USB_DFU_ON_BOOT=0", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/defines", "-I/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta", "-iprefix", "/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/includes", "-I/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/qio_qspi/include", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/variants/esp32s3", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/build_opt.h", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/file_opts", "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/esp32-hal-adc.c", "-o", "/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/core/esp32-hal-adc.c.o"], "file": "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/esp32-hal-adc.c"}, {"directory": "/home/<USER>", "arguments": ["/home/<USER>/.arduino15/packages/esp32/tools/esp-x32/2411/bin/xtensa-esp32s3-elf-gcc", "-M<PERSON>", "-c", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/c_flags", "-w", "-<PERSON><PERSON>", "-Werror=return-type", "-DF_CPU=240000000L", "-DARDUINO=10607", "-DARDUINO_ESP32S3_DEV", "-DARDUINO_ARCH_ESP32", "-DARDUINO_BOARD=\"ESP32S3_DEV\"", "-DARDUINO_VARIANT=\"esp32s3\"", "-DARDUINO_PARTITION_default", "-DARDUINO_HOST_OS=\"linux\"", "-DARDUINO_FQBN=\"esp32:esp32:esp32s3:UploadSpeed=921600,USBMode=hwcdc,CDCOnBoot=default,MSCOnBoot=default,DFUOnBoot=default,UploadMode=default,CPUFreq=240,FlashMode=qio,FlashSize=4M,PartitionScheme=default,DebugLevel=none,PSRAM=disabled,LoopCore=1,EventsCore=1,EraseFlash=none,JTAGAdapter=default,ZigbeeMode=default\"", "-DESP32=ESP32", "-DCORE_DEBUG_LEVEL=0", "-DARDUINO_RUNNING_CORE=1", "-DARDUINO_EVENT_RUNNING_CORE=1", "-DARDUINO_USB_MODE=1", "-DARDUINO_USB_CDC_ON_BOOT=0", "-DARDUINO_USB_MSC_ON_BOOT=0", "-DARDUINO_USB_DFU_ON_BOOT=0", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/defines", "-I/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta", "-iprefix", "/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/includes", "-I/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/qio_qspi/include", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/variants/esp32s3", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/build_opt.h", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/file_opts", "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/esp32-hal-bt.c", "-o", "/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/core/esp32-hal-bt.c.o"], "file": "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/esp32-hal-bt.c"}, {"directory": "/home/<USER>", "arguments": ["/home/<USER>/.arduino15/packages/esp32/tools/esp-x32/2411/bin/xtensa-esp32s3-elf-gcc", "-M<PERSON>", "-c", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/c_flags", "-w", "-<PERSON><PERSON>", "-Werror=return-type", "-DF_CPU=240000000L", "-DARDUINO=10607", "-DARDUINO_ESP32S3_DEV", "-DARDUINO_ARCH_ESP32", "-DARDUINO_BOARD=\"ESP32S3_DEV\"", "-DARDUINO_VARIANT=\"esp32s3\"", "-DARDUINO_PARTITION_default", "-DARDUINO_HOST_OS=\"linux\"", "-DARDUINO_FQBN=\"esp32:esp32:esp32s3:UploadSpeed=921600,USBMode=hwcdc,CDCOnBoot=default,MSCOnBoot=default,DFUOnBoot=default,UploadMode=default,CPUFreq=240,FlashMode=qio,FlashSize=4M,PartitionScheme=default,DebugLevel=none,PSRAM=disabled,LoopCore=1,EventsCore=1,EraseFlash=none,JTAGAdapter=default,ZigbeeMode=default\"", "-DESP32=ESP32", "-DCORE_DEBUG_LEVEL=0", "-DARDUINO_RUNNING_CORE=1", "-DARDUINO_EVENT_RUNNING_CORE=1", "-DARDUINO_USB_MODE=1", "-DARDUINO_USB_CDC_ON_BOOT=0", "-DARDUINO_USB_MSC_ON_BOOT=0", "-DARDUINO_USB_DFU_ON_BOOT=0", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/defines", "-I/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta", "-iprefix", "/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/includes", "-I/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/qio_qspi/include", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/variants/esp32s3", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/build_opt.h", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/file_opts", "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/esp32-hal-cpu.c", "-o", "/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/core/esp32-hal-cpu.c.o"], "file": "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/esp32-hal-cpu.c"}, {"directory": "/home/<USER>", "arguments": ["/home/<USER>/.arduino15/packages/esp32/tools/esp-x32/2411/bin/xtensa-esp32s3-elf-gcc", "-M<PERSON>", "-c", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/c_flags", "-w", "-<PERSON><PERSON>", "-Werror=return-type", "-DF_CPU=240000000L", "-DARDUINO=10607", "-DARDUINO_ESP32S3_DEV", "-DARDUINO_ARCH_ESP32", "-DARDUINO_BOARD=\"ESP32S3_DEV\"", "-DARDUINO_VARIANT=\"esp32s3\"", "-DARDUINO_PARTITION_default", "-DARDUINO_HOST_OS=\"linux\"", "-DARDUINO_FQBN=\"esp32:esp32:esp32s3:UploadSpeed=921600,USBMode=hwcdc,CDCOnBoot=default,MSCOnBoot=default,DFUOnBoot=default,UploadMode=default,CPUFreq=240,FlashMode=qio,FlashSize=4M,PartitionScheme=default,DebugLevel=none,PSRAM=disabled,LoopCore=1,EventsCore=1,EraseFlash=none,JTAGAdapter=default,ZigbeeMode=default\"", "-DESP32=ESP32", "-DCORE_DEBUG_LEVEL=0", "-DARDUINO_RUNNING_CORE=1", "-DARDUINO_EVENT_RUNNING_CORE=1", "-DARDUINO_USB_MODE=1", "-DARDUINO_USB_CDC_ON_BOOT=0", "-DARDUINO_USB_MSC_ON_BOOT=0", "-DARDUINO_USB_DFU_ON_BOOT=0", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/defines", "-I/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta", "-iprefix", "/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/includes", "-I/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/qio_qspi/include", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/variants/esp32s3", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/build_opt.h", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/file_opts", "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/esp32-hal-dac.c", "-o", "/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/core/esp32-hal-dac.c.o"], "file": "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/esp32-hal-dac.c"}, {"directory": "/home/<USER>", "arguments": ["/home/<USER>/.arduino15/packages/esp32/tools/esp-x32/2411/bin/xtensa-esp32s3-elf-gcc", "-M<PERSON>", "-c", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/c_flags", "-w", "-<PERSON><PERSON>", "-Werror=return-type", "-DF_CPU=240000000L", "-DARDUINO=10607", "-DARDUINO_ESP32S3_DEV", "-DARDUINO_ARCH_ESP32", "-DARDUINO_BOARD=\"ESP32S3_DEV\"", "-DARDUINO_VARIANT=\"esp32s3\"", "-DARDUINO_PARTITION_default", "-DARDUINO_HOST_OS=\"linux\"", "-DARDUINO_FQBN=\"esp32:esp32:esp32s3:UploadSpeed=921600,USBMode=hwcdc,CDCOnBoot=default,MSCOnBoot=default,DFUOnBoot=default,UploadMode=default,CPUFreq=240,FlashMode=qio,FlashSize=4M,PartitionScheme=default,DebugLevel=none,PSRAM=disabled,LoopCore=1,EventsCore=1,EraseFlash=none,JTAGAdapter=default,ZigbeeMode=default\"", "-DESP32=ESP32", "-DCORE_DEBUG_LEVEL=0", "-DARDUINO_RUNNING_CORE=1", "-DARDUINO_EVENT_RUNNING_CORE=1", "-DARDUINO_USB_MODE=1", "-DARDUINO_USB_CDC_ON_BOOT=0", "-DARDUINO_USB_MSC_ON_BOOT=0", "-DARDUINO_USB_DFU_ON_BOOT=0", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/defines", "-I/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta", "-iprefix", "/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/includes", "-I/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/qio_qspi/include", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/variants/esp32s3", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/build_opt.h", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/file_opts", "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/esp32-hal-gpio.c", "-o", "/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/core/esp32-hal-gpio.c.o"], "file": "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/esp32-hal-gpio.c"}, {"directory": "/home/<USER>", "arguments": ["/home/<USER>/.arduino15/packages/esp32/tools/esp-x32/2411/bin/xtensa-esp32s3-elf-gcc", "-M<PERSON>", "-c", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/c_flags", "-w", "-<PERSON><PERSON>", "-Werror=return-type", "-DF_CPU=240000000L", "-DARDUINO=10607", "-DARDUINO_ESP32S3_DEV", "-DARDUINO_ARCH_ESP32", "-DARDUINO_BOARD=\"ESP32S3_DEV\"", "-DARDUINO_VARIANT=\"esp32s3\"", "-DARDUINO_PARTITION_default", "-DARDUINO_HOST_OS=\"linux\"", "-DARDUINO_FQBN=\"esp32:esp32:esp32s3:UploadSpeed=921600,USBMode=hwcdc,CDCOnBoot=default,MSCOnBoot=default,DFUOnBoot=default,UploadMode=default,CPUFreq=240,FlashMode=qio,FlashSize=4M,PartitionScheme=default,DebugLevel=none,PSRAM=disabled,LoopCore=1,EventsCore=1,EraseFlash=none,JTAGAdapter=default,ZigbeeMode=default\"", "-DESP32=ESP32", "-DCORE_DEBUG_LEVEL=0", "-DARDUINO_RUNNING_CORE=1", "-DARDUINO_EVENT_RUNNING_CORE=1", "-DARDUINO_USB_MODE=1", "-DARDUINO_USB_CDC_ON_BOOT=0", "-DARDUINO_USB_MSC_ON_BOOT=0", "-DARDUINO_USB_DFU_ON_BOOT=0", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/defines", "-I/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta", "-iprefix", "/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/includes", "-I/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/qio_qspi/include", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/variants/esp32s3", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/build_opt.h", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/file_opts", "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/esp32-hal-i2c-ng.c", "-o", "/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/core/esp32-hal-i2c-ng.c.o"], "file": "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/esp32-hal-i2c-ng.c"}, {"directory": "/home/<USER>", "arguments": ["/home/<USER>/.arduino15/packages/esp32/tools/esp-x32/2411/bin/xtensa-esp32s3-elf-gcc", "-M<PERSON>", "-c", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/c_flags", "-w", "-<PERSON><PERSON>", "-Werror=return-type", "-DF_CPU=240000000L", "-DARDUINO=10607", "-DARDUINO_ESP32S3_DEV", "-DARDUINO_ARCH_ESP32", "-DARDUINO_BOARD=\"ESP32S3_DEV\"", "-DARDUINO_VARIANT=\"esp32s3\"", "-DARDUINO_PARTITION_default", "-DARDUINO_HOST_OS=\"linux\"", "-DARDUINO_FQBN=\"esp32:esp32:esp32s3:UploadSpeed=921600,USBMode=hwcdc,CDCOnBoot=default,MSCOnBoot=default,DFUOnBoot=default,UploadMode=default,CPUFreq=240,FlashMode=qio,FlashSize=4M,PartitionScheme=default,DebugLevel=none,PSRAM=disabled,LoopCore=1,EventsCore=1,EraseFlash=none,JTAGAdapter=default,ZigbeeMode=default\"", "-DESP32=ESP32", "-DCORE_DEBUG_LEVEL=0", "-DARDUINO_RUNNING_CORE=1", "-DARDUINO_EVENT_RUNNING_CORE=1", "-DARDUINO_USB_MODE=1", "-DARDUINO_USB_CDC_ON_BOOT=0", "-DARDUINO_USB_MSC_ON_BOOT=0", "-DARDUINO_USB_DFU_ON_BOOT=0", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/defines", "-I/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta", "-iprefix", "/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/includes", "-I/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/qio_qspi/include", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/variants/esp32s3", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/build_opt.h", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/file_opts", "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/esp32-hal-i2c-slave.c", "-o", "/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/core/esp32-hal-i2c-slave.c.o"], "file": "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/esp32-hal-i2c-slave.c"}, {"directory": "/home/<USER>", "arguments": ["/home/<USER>/.arduino15/packages/esp32/tools/esp-x32/2411/bin/xtensa-esp32s3-elf-gcc", "-M<PERSON>", "-c", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/c_flags", "-w", "-<PERSON><PERSON>", "-Werror=return-type", "-DF_CPU=240000000L", "-DARDUINO=10607", "-DARDUINO_ESP32S3_DEV", "-DARDUINO_ARCH_ESP32", "-DARDUINO_BOARD=\"ESP32S3_DEV\"", "-DARDUINO_VARIANT=\"esp32s3\"", "-DARDUINO_PARTITION_default", "-DARDUINO_HOST_OS=\"linux\"", "-DARDUINO_FQBN=\"esp32:esp32:esp32s3:UploadSpeed=921600,USBMode=hwcdc,CDCOnBoot=default,MSCOnBoot=default,DFUOnBoot=default,UploadMode=default,CPUFreq=240,FlashMode=qio,FlashSize=4M,PartitionScheme=default,DebugLevel=none,PSRAM=disabled,LoopCore=1,EventsCore=1,EraseFlash=none,JTAGAdapter=default,ZigbeeMode=default\"", "-DESP32=ESP32", "-DCORE_DEBUG_LEVEL=0", "-DARDUINO_RUNNING_CORE=1", "-DARDUINO_EVENT_RUNNING_CORE=1", "-DARDUINO_USB_MODE=1", "-DARDUINO_USB_CDC_ON_BOOT=0", "-DARDUINO_USB_MSC_ON_BOOT=0", "-DARDUINO_USB_DFU_ON_BOOT=0", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/defines", "-I/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta", "-iprefix", "/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/includes", "-I/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/qio_qspi/include", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/variants/esp32s3", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/build_opt.h", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/file_opts", "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/esp32-hal-i2c.c", "-o", "/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/core/esp32-hal-i2c.c.o"], "file": "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/esp32-hal-i2c.c"}, {"directory": "/home/<USER>", "arguments": ["/home/<USER>/.arduino15/packages/esp32/tools/esp-x32/2411/bin/xtensa-esp32s3-elf-gcc", "-M<PERSON>", "-c", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/c_flags", "-w", "-<PERSON><PERSON>", "-Werror=return-type", "-DF_CPU=240000000L", "-DARDUINO=10607", "-DARDUINO_ESP32S3_DEV", "-DARDUINO_ARCH_ESP32", "-DARDUINO_BOARD=\"ESP32S3_DEV\"", "-DARDUINO_VARIANT=\"esp32s3\"", "-DARDUINO_PARTITION_default", "-DARDUINO_HOST_OS=\"linux\"", "-DARDUINO_FQBN=\"esp32:esp32:esp32s3:UploadSpeed=921600,USBMode=hwcdc,CDCOnBoot=default,MSCOnBoot=default,DFUOnBoot=default,UploadMode=default,CPUFreq=240,FlashMode=qio,FlashSize=4M,PartitionScheme=default,DebugLevel=none,PSRAM=disabled,LoopCore=1,EventsCore=1,EraseFlash=none,JTAGAdapter=default,ZigbeeMode=default\"", "-DESP32=ESP32", "-DCORE_DEBUG_LEVEL=0", "-DARDUINO_RUNNING_CORE=1", "-DARDUINO_EVENT_RUNNING_CORE=1", "-DARDUINO_USB_MODE=1", "-DARDUINO_USB_CDC_ON_BOOT=0", "-DARDUINO_USB_MSC_ON_BOOT=0", "-DARDUINO_USB_DFU_ON_BOOT=0", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/defines", "-I/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta", "-iprefix", "/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/includes", "-I/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/qio_qspi/include", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/variants/esp32s3", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/build_opt.h", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/file_opts", "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/esp32-hal-ledc.c", "-o", "/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/core/esp32-hal-ledc.c.o"], "file": "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/esp32-hal-ledc.c"}, {"directory": "/home/<USER>", "arguments": ["/home/<USER>/.arduino15/packages/esp32/tools/esp-x32/2411/bin/xtensa-esp32s3-elf-gcc", "-M<PERSON>", "-c", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/c_flags", "-w", "-<PERSON><PERSON>", "-Werror=return-type", "-DF_CPU=240000000L", "-DARDUINO=10607", "-DARDUINO_ESP32S3_DEV", "-DARDUINO_ARCH_ESP32", "-DARDUINO_BOARD=\"ESP32S3_DEV\"", "-DARDUINO_VARIANT=\"esp32s3\"", "-DARDUINO_PARTITION_default", "-DARDUINO_HOST_OS=\"linux\"", "-DARDUINO_FQBN=\"esp32:esp32:esp32s3:UploadSpeed=921600,USBMode=hwcdc,CDCOnBoot=default,MSCOnBoot=default,DFUOnBoot=default,UploadMode=default,CPUFreq=240,FlashMode=qio,FlashSize=4M,PartitionScheme=default,DebugLevel=none,PSRAM=disabled,LoopCore=1,EventsCore=1,EraseFlash=none,JTAGAdapter=default,ZigbeeMode=default\"", "-DESP32=ESP32", "-DCORE_DEBUG_LEVEL=0", "-DARDUINO_RUNNING_CORE=1", "-DARDUINO_EVENT_RUNNING_CORE=1", "-DARDUINO_USB_MODE=1", "-DARDUINO_USB_CDC_ON_BOOT=0", "-DARDUINO_USB_MSC_ON_BOOT=0", "-DARDUINO_USB_DFU_ON_BOOT=0", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/defines", "-I/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta", "-iprefix", "/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/includes", "-I/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/qio_qspi/include", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/variants/esp32s3", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/build_opt.h", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/file_opts", "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/esp32-hal-matrix.c", "-o", "/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/core/esp32-hal-matrix.c.o"], "file": "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/esp32-hal-matrix.c"}, {"directory": "/home/<USER>", "arguments": ["/home/<USER>/.arduino15/packages/esp32/tools/esp-x32/2411/bin/xtensa-esp32s3-elf-gcc", "-M<PERSON>", "-c", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/c_flags", "-w", "-<PERSON><PERSON>", "-Werror=return-type", "-DF_CPU=240000000L", "-DARDUINO=10607", "-DARDUINO_ESP32S3_DEV", "-DARDUINO_ARCH_ESP32", "-DARDUINO_BOARD=\"ESP32S3_DEV\"", "-DARDUINO_VARIANT=\"esp32s3\"", "-DARDUINO_PARTITION_default", "-DARDUINO_HOST_OS=\"linux\"", "-DARDUINO_FQBN=\"esp32:esp32:esp32s3:UploadSpeed=921600,USBMode=hwcdc,CDCOnBoot=default,MSCOnBoot=default,DFUOnBoot=default,UploadMode=default,CPUFreq=240,FlashMode=qio,FlashSize=4M,PartitionScheme=default,DebugLevel=none,PSRAM=disabled,LoopCore=1,EventsCore=1,EraseFlash=none,JTAGAdapter=default,ZigbeeMode=default\"", "-DESP32=ESP32", "-DCORE_DEBUG_LEVEL=0", "-DARDUINO_RUNNING_CORE=1", "-DARDUINO_EVENT_RUNNING_CORE=1", "-DARDUINO_USB_MODE=1", "-DARDUINO_USB_CDC_ON_BOOT=0", "-DARDUINO_USB_MSC_ON_BOOT=0", "-DARDUINO_USB_DFU_ON_BOOT=0", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/defines", "-I/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta", "-iprefix", "/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/includes", "-I/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/qio_qspi/include", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/variants/esp32s3", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/build_opt.h", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/file_opts", "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/esp32-hal-misc.c", "-o", "/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/core/esp32-hal-misc.c.o"], "file": "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/esp32-hal-misc.c"}, {"directory": "/home/<USER>", "arguments": ["/home/<USER>/.arduino15/packages/esp32/tools/esp-x32/2411/bin/xtensa-esp32s3-elf-gcc", "-M<PERSON>", "-c", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/c_flags", "-w", "-<PERSON><PERSON>", "-Werror=return-type", "-DF_CPU=240000000L", "-DARDUINO=10607", "-DARDUINO_ESP32S3_DEV", "-DARDUINO_ARCH_ESP32", "-DARDUINO_BOARD=\"ESP32S3_DEV\"", "-DARDUINO_VARIANT=\"esp32s3\"", "-DARDUINO_PARTITION_default", "-DARDUINO_HOST_OS=\"linux\"", "-DARDUINO_FQBN=\"esp32:esp32:esp32s3:UploadSpeed=921600,USBMode=hwcdc,CDCOnBoot=default,MSCOnBoot=default,DFUOnBoot=default,UploadMode=default,CPUFreq=240,FlashMode=qio,FlashSize=4M,PartitionScheme=default,DebugLevel=none,PSRAM=disabled,LoopCore=1,EventsCore=1,EraseFlash=none,JTAGAdapter=default,ZigbeeMode=default\"", "-DESP32=ESP32", "-DCORE_DEBUG_LEVEL=0", "-DARDUINO_RUNNING_CORE=1", "-DARDUINO_EVENT_RUNNING_CORE=1", "-DARDUINO_USB_MODE=1", "-DARDUINO_USB_CDC_ON_BOOT=0", "-DARDUINO_USB_MSC_ON_BOOT=0", "-DARDUINO_USB_DFU_ON_BOOT=0", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/defines", "-I/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta", "-iprefix", "/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/includes", "-I/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/qio_qspi/include", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/variants/esp32s3", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/build_opt.h", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/file_opts", "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/esp32-hal-periman.c", "-o", "/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/core/esp32-hal-periman.c.o"], "file": "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/esp32-hal-periman.c"}, {"directory": "/home/<USER>", "arguments": ["/home/<USER>/.arduino15/packages/esp32/tools/esp-x32/2411/bin/xtensa-esp32s3-elf-gcc", "-M<PERSON>", "-c", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/c_flags", "-w", "-<PERSON><PERSON>", "-Werror=return-type", "-DF_CPU=240000000L", "-DARDUINO=10607", "-DARDUINO_ESP32S3_DEV", "-DARDUINO_ARCH_ESP32", "-DARDUINO_BOARD=\"ESP32S3_DEV\"", "-DARDUINO_VARIANT=\"esp32s3\"", "-DARDUINO_PARTITION_default", "-DARDUINO_HOST_OS=\"linux\"", "-DARDUINO_FQBN=\"esp32:esp32:esp32s3:UploadSpeed=921600,USBMode=hwcdc,CDCOnBoot=default,MSCOnBoot=default,DFUOnBoot=default,UploadMode=default,CPUFreq=240,FlashMode=qio,FlashSize=4M,PartitionScheme=default,DebugLevel=none,PSRAM=disabled,LoopCore=1,EventsCore=1,EraseFlash=none,JTAGAdapter=default,ZigbeeMode=default\"", "-DESP32=ESP32", "-DCORE_DEBUG_LEVEL=0", "-DARDUINO_RUNNING_CORE=1", "-DARDUINO_EVENT_RUNNING_CORE=1", "-DARDUINO_USB_MODE=1", "-DARDUINO_USB_CDC_ON_BOOT=0", "-DARDUINO_USB_MSC_ON_BOOT=0", "-DARDUINO_USB_DFU_ON_BOOT=0", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/defines", "-I/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta", "-iprefix", "/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/includes", "-I/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/qio_qspi/include", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/variants/esp32s3", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/build_opt.h", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/file_opts", "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/esp32-hal-psram.c", "-o", "/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/core/esp32-hal-psram.c.o"], "file": "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/esp32-hal-psram.c"}, {"directory": "/home/<USER>", "arguments": ["/home/<USER>/.arduino15/packages/esp32/tools/esp-x32/2411/bin/xtensa-esp32s3-elf-gcc", "-M<PERSON>", "-c", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/c_flags", "-w", "-<PERSON><PERSON>", "-Werror=return-type", "-DF_CPU=240000000L", "-DARDUINO=10607", "-DARDUINO_ESP32S3_DEV", "-DARDUINO_ARCH_ESP32", "-DARDUINO_BOARD=\"ESP32S3_DEV\"", "-DARDUINO_VARIANT=\"esp32s3\"", "-DARDUINO_PARTITION_default", "-DARDUINO_HOST_OS=\"linux\"", "-DARDUINO_FQBN=\"esp32:esp32:esp32s3:UploadSpeed=921600,USBMode=hwcdc,CDCOnBoot=default,MSCOnBoot=default,DFUOnBoot=default,UploadMode=default,CPUFreq=240,FlashMode=qio,FlashSize=4M,PartitionScheme=default,DebugLevel=none,PSRAM=disabled,LoopCore=1,EventsCore=1,EraseFlash=none,JTAGAdapter=default,ZigbeeMode=default\"", "-DESP32=ESP32", "-DCORE_DEBUG_LEVEL=0", "-DARDUINO_RUNNING_CORE=1", "-DARDUINO_EVENT_RUNNING_CORE=1", "-DARDUINO_USB_MODE=1", "-DARDUINO_USB_CDC_ON_BOOT=0", "-DARDUINO_USB_MSC_ON_BOOT=0", "-DARDUINO_USB_DFU_ON_BOOT=0", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/defines", "-I/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta", "-iprefix", "/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/includes", "-I/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/qio_qspi/include", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/variants/esp32s3", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/build_opt.h", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/file_opts", "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/esp32-hal-rgb-led.c", "-o", "/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/core/esp32-hal-rgb-led.c.o"], "file": "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/esp32-hal-rgb-led.c"}, {"directory": "/home/<USER>", "arguments": ["/home/<USER>/.arduino15/packages/esp32/tools/esp-x32/2411/bin/xtensa-esp32s3-elf-gcc", "-M<PERSON>", "-c", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/c_flags", "-w", "-<PERSON><PERSON>", "-Werror=return-type", "-DF_CPU=240000000L", "-DARDUINO=10607", "-DARDUINO_ESP32S3_DEV", "-DARDUINO_ARCH_ESP32", "-DARDUINO_BOARD=\"ESP32S3_DEV\"", "-DARDUINO_VARIANT=\"esp32s3\"", "-DARDUINO_PARTITION_default", "-DARDUINO_HOST_OS=\"linux\"", "-DARDUINO_FQBN=\"esp32:esp32:esp32s3:UploadSpeed=921600,USBMode=hwcdc,CDCOnBoot=default,MSCOnBoot=default,DFUOnBoot=default,UploadMode=default,CPUFreq=240,FlashMode=qio,FlashSize=4M,PartitionScheme=default,DebugLevel=none,PSRAM=disabled,LoopCore=1,EventsCore=1,EraseFlash=none,JTAGAdapter=default,ZigbeeMode=default\"", "-DESP32=ESP32", "-DCORE_DEBUG_LEVEL=0", "-DARDUINO_RUNNING_CORE=1", "-DARDUINO_EVENT_RUNNING_CORE=1", "-DARDUINO_USB_MODE=1", "-DARDUINO_USB_CDC_ON_BOOT=0", "-DARDUINO_USB_MSC_ON_BOOT=0", "-DARDUINO_USB_DFU_ON_BOOT=0", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/defines", "-I/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta", "-iprefix", "/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/includes", "-I/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/qio_qspi/include", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/variants/esp32s3", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/build_opt.h", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/file_opts", "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/esp32-hal-rmt.c", "-o", "/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/core/esp32-hal-rmt.c.o"], "file": "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/esp32-hal-rmt.c"}, {"directory": "/home/<USER>", "arguments": ["/home/<USER>/.arduino15/packages/esp32/tools/esp-x32/2411/bin/xtensa-esp32s3-elf-gcc", "-M<PERSON>", "-c", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/c_flags", "-w", "-<PERSON><PERSON>", "-Werror=return-type", "-DF_CPU=240000000L", "-DARDUINO=10607", "-DARDUINO_ESP32S3_DEV", "-DARDUINO_ARCH_ESP32", "-DARDUINO_BOARD=\"ESP32S3_DEV\"", "-DARDUINO_VARIANT=\"esp32s3\"", "-DARDUINO_PARTITION_default", "-DARDUINO_HOST_OS=\"linux\"", "-DARDUINO_FQBN=\"esp32:esp32:esp32s3:UploadSpeed=921600,USBMode=hwcdc,CDCOnBoot=default,MSCOnBoot=default,DFUOnBoot=default,UploadMode=default,CPUFreq=240,FlashMode=qio,FlashSize=4M,PartitionScheme=default,DebugLevel=none,PSRAM=disabled,LoopCore=1,EventsCore=1,EraseFlash=none,JTAGAdapter=default,ZigbeeMode=default\"", "-DESP32=ESP32", "-DCORE_DEBUG_LEVEL=0", "-DARDUINO_RUNNING_CORE=1", "-DARDUINO_EVENT_RUNNING_CORE=1", "-DARDUINO_USB_MODE=1", "-DARDUINO_USB_CDC_ON_BOOT=0", "-DARDUINO_USB_MSC_ON_BOOT=0", "-DARDUINO_USB_DFU_ON_BOOT=0", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/defines", "-I/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta", "-iprefix", "/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/includes", "-I/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/qio_qspi/include", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/variants/esp32s3", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/build_opt.h", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/file_opts", "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/esp32-hal-sigmadelta.c", "-o", "/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/core/esp32-hal-sigmadelta.c.o"], "file": "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/esp32-hal-sigmadelta.c"}, {"directory": "/home/<USER>", "arguments": ["/home/<USER>/.arduino15/packages/esp32/tools/esp-x32/2411/bin/xtensa-esp32s3-elf-gcc", "-M<PERSON>", "-c", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/c_flags", "-w", "-<PERSON><PERSON>", "-Werror=return-type", "-DF_CPU=240000000L", "-DARDUINO=10607", "-DARDUINO_ESP32S3_DEV", "-DARDUINO_ARCH_ESP32", "-DARDUINO_BOARD=\"ESP32S3_DEV\"", "-DARDUINO_VARIANT=\"esp32s3\"", "-DARDUINO_PARTITION_default", "-DARDUINO_HOST_OS=\"linux\"", "-DARDUINO_FQBN=\"esp32:esp32:esp32s3:UploadSpeed=921600,USBMode=hwcdc,CDCOnBoot=default,MSCOnBoot=default,DFUOnBoot=default,UploadMode=default,CPUFreq=240,FlashMode=qio,FlashSize=4M,PartitionScheme=default,DebugLevel=none,PSRAM=disabled,LoopCore=1,EventsCore=1,EraseFlash=none,JTAGAdapter=default,ZigbeeMode=default\"", "-DESP32=ESP32", "-DCORE_DEBUG_LEVEL=0", "-DARDUINO_RUNNING_CORE=1", "-DARDUINO_EVENT_RUNNING_CORE=1", "-DARDUINO_USB_MODE=1", "-DARDUINO_USB_CDC_ON_BOOT=0", "-DARDUINO_USB_MSC_ON_BOOT=0", "-DARDUINO_USB_DFU_ON_BOOT=0", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/defines", "-I/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta", "-iprefix", "/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/includes", "-I/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/qio_qspi/include", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/variants/esp32s3", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/build_opt.h", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/file_opts", "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/esp32-hal-spi.c", "-o", "/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/core/esp32-hal-spi.c.o"], "file": "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/esp32-hal-spi.c"}, {"directory": "/home/<USER>", "arguments": ["/home/<USER>/.arduino15/packages/esp32/tools/esp-x32/2411/bin/xtensa-esp32s3-elf-gcc", "-M<PERSON>", "-c", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/c_flags", "-w", "-<PERSON><PERSON>", "-Werror=return-type", "-DF_CPU=240000000L", "-DARDUINO=10607", "-DARDUINO_ESP32S3_DEV", "-DARDUINO_ARCH_ESP32", "-DARDUINO_BOARD=\"ESP32S3_DEV\"", "-DARDUINO_VARIANT=\"esp32s3\"", "-DARDUINO_PARTITION_default", "-DARDUINO_HOST_OS=\"linux\"", "-DARDUINO_FQBN=\"esp32:esp32:esp32s3:UploadSpeed=921600,USBMode=hwcdc,CDCOnBoot=default,MSCOnBoot=default,DFUOnBoot=default,UploadMode=default,CPUFreq=240,FlashMode=qio,FlashSize=4M,PartitionScheme=default,DebugLevel=none,PSRAM=disabled,LoopCore=1,EventsCore=1,EraseFlash=none,JTAGAdapter=default,ZigbeeMode=default\"", "-DESP32=ESP32", "-DCORE_DEBUG_LEVEL=0", "-DARDUINO_RUNNING_CORE=1", "-DARDUINO_EVENT_RUNNING_CORE=1", "-DARDUINO_USB_MODE=1", "-DARDUINO_USB_CDC_ON_BOOT=0", "-DARDUINO_USB_MSC_ON_BOOT=0", "-DARDUINO_USB_DFU_ON_BOOT=0", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/defines", "-I/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta", "-iprefix", "/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/includes", "-I/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/qio_qspi/include", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/variants/esp32s3", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/build_opt.h", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/file_opts", "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/esp32-hal-time.c", "-o", "/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/core/esp32-hal-time.c.o"], "file": "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/esp32-hal-time.c"}, {"directory": "/home/<USER>", "arguments": ["/home/<USER>/.arduino15/packages/esp32/tools/esp-x32/2411/bin/xtensa-esp32s3-elf-gcc", "-M<PERSON>", "-c", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/c_flags", "-w", "-<PERSON><PERSON>", "-Werror=return-type", "-DF_CPU=240000000L", "-DARDUINO=10607", "-DARDUINO_ESP32S3_DEV", "-DARDUINO_ARCH_ESP32", "-DARDUINO_BOARD=\"ESP32S3_DEV\"", "-DARDUINO_VARIANT=\"esp32s3\"", "-DARDUINO_PARTITION_default", "-DARDUINO_HOST_OS=\"linux\"", "-DARDUINO_FQBN=\"esp32:esp32:esp32s3:UploadSpeed=921600,USBMode=hwcdc,CDCOnBoot=default,MSCOnBoot=default,DFUOnBoot=default,UploadMode=default,CPUFreq=240,FlashMode=qio,FlashSize=4M,PartitionScheme=default,DebugLevel=none,PSRAM=disabled,LoopCore=1,EventsCore=1,EraseFlash=none,JTAGAdapter=default,ZigbeeMode=default\"", "-DESP32=ESP32", "-DCORE_DEBUG_LEVEL=0", "-DARDUINO_RUNNING_CORE=1", "-DARDUINO_EVENT_RUNNING_CORE=1", "-DARDUINO_USB_MODE=1", "-DARDUINO_USB_CDC_ON_BOOT=0", "-DARDUINO_USB_MSC_ON_BOOT=0", "-DARDUINO_USB_DFU_ON_BOOT=0", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/defines", "-I/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta", "-iprefix", "/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/includes", "-I/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/qio_qspi/include", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/variants/esp32s3", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/build_opt.h", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/file_opts", "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/esp32-hal-timer.c", "-o", "/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/core/esp32-hal-timer.c.o"], "file": "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/esp32-hal-timer.c"}, {"directory": "/home/<USER>", "arguments": ["/home/<USER>/.arduino15/packages/esp32/tools/esp-x32/2411/bin/xtensa-esp32s3-elf-gcc", "-M<PERSON>", "-c", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/c_flags", "-w", "-<PERSON><PERSON>", "-Werror=return-type", "-DF_CPU=240000000L", "-DARDUINO=10607", "-DARDUINO_ESP32S3_DEV", "-DARDUINO_ARCH_ESP32", "-DARDUINO_BOARD=\"ESP32S3_DEV\"", "-DARDUINO_VARIANT=\"esp32s3\"", "-DARDUINO_PARTITION_default", "-DARDUINO_HOST_OS=\"linux\"", "-DARDUINO_FQBN=\"esp32:esp32:esp32s3:UploadSpeed=921600,USBMode=hwcdc,CDCOnBoot=default,MSCOnBoot=default,DFUOnBoot=default,UploadMode=default,CPUFreq=240,FlashMode=qio,FlashSize=4M,PartitionScheme=default,DebugLevel=none,PSRAM=disabled,LoopCore=1,EventsCore=1,EraseFlash=none,JTAGAdapter=default,ZigbeeMode=default\"", "-DESP32=ESP32", "-DCORE_DEBUG_LEVEL=0", "-DARDUINO_RUNNING_CORE=1", "-DARDUINO_EVENT_RUNNING_CORE=1", "-DARDUINO_USB_MODE=1", "-DARDUINO_USB_CDC_ON_BOOT=0", "-DARDUINO_USB_MSC_ON_BOOT=0", "-DARDUINO_USB_DFU_ON_BOOT=0", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/defines", "-I/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta", "-iprefix", "/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/includes", "-I/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/qio_qspi/include", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/variants/esp32s3", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/build_opt.h", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/file_opts", "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/esp32-hal-tinyusb.c", "-o", "/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/core/esp32-hal-tinyusb.c.o"], "file": "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/esp32-hal-tinyusb.c"}, {"directory": "/home/<USER>", "arguments": ["/home/<USER>/.arduino15/packages/esp32/tools/esp-x32/2411/bin/xtensa-esp32s3-elf-gcc", "-M<PERSON>", "-c", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/c_flags", "-w", "-<PERSON><PERSON>", "-Werror=return-type", "-DF_CPU=240000000L", "-DARDUINO=10607", "-DARDUINO_ESP32S3_DEV", "-DARDUINO_ARCH_ESP32", "-DARDUINO_BOARD=\"ESP32S3_DEV\"", "-DARDUINO_VARIANT=\"esp32s3\"", "-DARDUINO_PARTITION_default", "-DARDUINO_HOST_OS=\"linux\"", "-DARDUINO_FQBN=\"esp32:esp32:esp32s3:UploadSpeed=921600,USBMode=hwcdc,CDCOnBoot=default,MSCOnBoot=default,DFUOnBoot=default,UploadMode=default,CPUFreq=240,FlashMode=qio,FlashSize=4M,PartitionScheme=default,DebugLevel=none,PSRAM=disabled,LoopCore=1,EventsCore=1,EraseFlash=none,JTAGAdapter=default,ZigbeeMode=default\"", "-DESP32=ESP32", "-DCORE_DEBUG_LEVEL=0", "-DARDUINO_RUNNING_CORE=1", "-DARDUINO_EVENT_RUNNING_CORE=1", "-DARDUINO_USB_MODE=1", "-DARDUINO_USB_CDC_ON_BOOT=0", "-DARDUINO_USB_MSC_ON_BOOT=0", "-DARDUINO_USB_DFU_ON_BOOT=0", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/defines", "-I/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta", "-iprefix", "/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/includes", "-I/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/qio_qspi/include", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/variants/esp32s3", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/build_opt.h", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/file_opts", "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/esp32-hal-touch-ng.c", "-o", "/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/core/esp32-hal-touch-ng.c.o"], "file": "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/esp32-hal-touch-ng.c"}, {"directory": "/home/<USER>", "arguments": ["/home/<USER>/.arduino15/packages/esp32/tools/esp-x32/2411/bin/xtensa-esp32s3-elf-gcc", "-M<PERSON>", "-c", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/c_flags", "-w", "-<PERSON><PERSON>", "-Werror=return-type", "-DF_CPU=240000000L", "-DARDUINO=10607", "-DARDUINO_ESP32S3_DEV", "-DARDUINO_ARCH_ESP32", "-DARDUINO_BOARD=\"ESP32S3_DEV\"", "-DARDUINO_VARIANT=\"esp32s3\"", "-DARDUINO_PARTITION_default", "-DARDUINO_HOST_OS=\"linux\"", "-DARDUINO_FQBN=\"esp32:esp32:esp32s3:UploadSpeed=921600,USBMode=hwcdc,CDCOnBoot=default,MSCOnBoot=default,DFUOnBoot=default,UploadMode=default,CPUFreq=240,FlashMode=qio,FlashSize=4M,PartitionScheme=default,DebugLevel=none,PSRAM=disabled,LoopCore=1,EventsCore=1,EraseFlash=none,JTAGAdapter=default,ZigbeeMode=default\"", "-DESP32=ESP32", "-DCORE_DEBUG_LEVEL=0", "-DARDUINO_RUNNING_CORE=1", "-DARDUINO_EVENT_RUNNING_CORE=1", "-DARDUINO_USB_MODE=1", "-DARDUINO_USB_CDC_ON_BOOT=0", "-DARDUINO_USB_MSC_ON_BOOT=0", "-DARDUINO_USB_DFU_ON_BOOT=0", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/defines", "-I/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta", "-iprefix", "/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/includes", "-I/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/qio_qspi/include", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/variants/esp32s3", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/build_opt.h", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/file_opts", "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/esp32-hal-touch.c", "-o", "/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/core/esp32-hal-touch.c.o"], "file": "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/esp32-hal-touch.c"}, {"directory": "/home/<USER>", "arguments": ["/home/<USER>/.arduino15/packages/esp32/tools/esp-x32/2411/bin/xtensa-esp32s3-elf-gcc", "-M<PERSON>", "-c", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/c_flags", "-w", "-<PERSON><PERSON>", "-Werror=return-type", "-DF_CPU=240000000L", "-DARDUINO=10607", "-DARDUINO_ESP32S3_DEV", "-DARDUINO_ARCH_ESP32", "-DARDUINO_BOARD=\"ESP32S3_DEV\"", "-DARDUINO_VARIANT=\"esp32s3\"", "-DARDUINO_PARTITION_default", "-DARDUINO_HOST_OS=\"linux\"", "-DARDUINO_FQBN=\"esp32:esp32:esp32s3:UploadSpeed=921600,USBMode=hwcdc,CDCOnBoot=default,MSCOnBoot=default,DFUOnBoot=default,UploadMode=default,CPUFreq=240,FlashMode=qio,FlashSize=4M,PartitionScheme=default,DebugLevel=none,PSRAM=disabled,LoopCore=1,EventsCore=1,EraseFlash=none,JTAGAdapter=default,ZigbeeMode=default\"", "-DESP32=ESP32", "-DCORE_DEBUG_LEVEL=0", "-DARDUINO_RUNNING_CORE=1", "-DARDUINO_EVENT_RUNNING_CORE=1", "-DARDUINO_USB_MODE=1", "-DARDUINO_USB_CDC_ON_BOOT=0", "-DARDUINO_USB_MSC_ON_BOOT=0", "-DARDUINO_USB_DFU_ON_BOOT=0", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/defines", "-I/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta", "-iprefix", "/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/includes", "-I/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/qio_qspi/include", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/variants/esp32s3", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/build_opt.h", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/file_opts", "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/esp32-hal-uart.c", "-o", "/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/core/esp32-hal-uart.c.o"], "file": "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/esp32-hal-uart.c"}, {"directory": "/home/<USER>", "arguments": ["/home/<USER>/.arduino15/packages/esp32/tools/esp-x32/2411/bin/xtensa-esp32s3-elf-gcc", "-M<PERSON>", "-c", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/c_flags", "-w", "-<PERSON><PERSON>", "-Werror=return-type", "-DF_CPU=240000000L", "-DARDUINO=10607", "-DARDUINO_ESP32S3_DEV", "-DARDUINO_ARCH_ESP32", "-DARDUINO_BOARD=\"ESP32S3_DEV\"", "-DARDUINO_VARIANT=\"esp32s3\"", "-DARDUINO_PARTITION_default", "-DARDUINO_HOST_OS=\"linux\"", "-DARDUINO_FQBN=\"esp32:esp32:esp32s3:UploadSpeed=921600,USBMode=hwcdc,CDCOnBoot=default,MSCOnBoot=default,DFUOnBoot=default,UploadMode=default,CPUFreq=240,FlashMode=qio,FlashSize=4M,PartitionScheme=default,DebugLevel=none,PSRAM=disabled,LoopCore=1,EventsCore=1,EraseFlash=none,JTAGAdapter=default,ZigbeeMode=default\"", "-DESP32=ESP32", "-DCORE_DEBUG_LEVEL=0", "-DARDUINO_RUNNING_CORE=1", "-DARDUINO_EVENT_RUNNING_CORE=1", "-DARDUINO_USB_MODE=1", "-DARDUINO_USB_CDC_ON_BOOT=0", "-DARDUINO_USB_MSC_ON_BOOT=0", "-DARDUINO_USB_DFU_ON_BOOT=0", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/defines", "-I/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta", "-iprefix", "/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/includes", "-I/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/qio_qspi/include", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/variants/esp32s3", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/build_opt.h", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/file_opts", "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/firmware_msc_fat.c", "-o", "/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/core/firmware_msc_fat.c.o"], "file": "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/firmware_msc_fat.c"}, {"directory": "/home/<USER>", "arguments": ["/home/<USER>/.arduino15/packages/esp32/tools/esp-x32/2411/bin/xtensa-esp32s3-elf-g++", "-M<PERSON>", "-c", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/cpp_flags", "-w", "-<PERSON><PERSON>", "-Werror=return-type", "-DF_CPU=240000000L", "-DARDUINO=10607", "-DARDUINO_ESP32S3_DEV", "-DARDUINO_ARCH_ESP32", "-DARDUINO_BOARD=\"ESP32S3_DEV\"", "-DARDUINO_VARIANT=\"esp32s3\"", "-DARDUINO_PARTITION_default", "-DARDUINO_HOST_OS=\"linux\"", "-DARDUINO_FQBN=\"esp32:esp32:esp32s3:UploadSpeed=921600,USBMode=hwcdc,CDCOnBoot=default,MSCOnBoot=default,DFUOnBoot=default,UploadMode=default,CPUFreq=240,FlashMode=qio,FlashSize=4M,PartitionScheme=default,DebugLevel=none,PSRAM=disabled,LoopCore=1,EventsCore=1,EraseFlash=none,JTAGAdapter=default,ZigbeeMode=default\"", "-DESP32=ESP32", "-DCORE_DEBUG_LEVEL=0", "-DARDUINO_RUNNING_CORE=1", "-DARDUINO_EVENT_RUNNING_CORE=1", "-DARDUINO_USB_MODE=1", "-DARDUINO_USB_CDC_ON_BOOT=0", "-DARDUINO_USB_MSC_ON_BOOT=0", "-DARDUINO_USB_DFU_ON_BOOT=0", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/defines", "-I/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta", "-iprefix", "/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/includes", "-I/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/qio_qspi/include", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/variants/esp32s3", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/build_opt.h", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/file_opts", "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/freertos_stats.cpp", "-o", "/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/core/freertos_stats.cpp.o"], "file": "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/freertos_stats.cpp"}, {"directory": "/home/<USER>", "arguments": ["/home/<USER>/.arduino15/packages/esp32/tools/esp-x32/2411/bin/xtensa-esp32s3-elf-gcc", "-M<PERSON>", "-c", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/c_flags", "-w", "-<PERSON><PERSON>", "-Werror=return-type", "-DF_CPU=240000000L", "-DARDUINO=10607", "-DARDUINO_ESP32S3_DEV", "-DARDUINO_ARCH_ESP32", "-DARDUINO_BOARD=\"ESP32S3_DEV\"", "-DARDUINO_VARIANT=\"esp32s3\"", "-DARDUINO_PARTITION_default", "-DARDUINO_HOST_OS=\"linux\"", "-DARDUINO_FQBN=\"esp32:esp32:esp32s3:UploadSpeed=921600,USBMode=hwcdc,CDCOnBoot=default,MSCOnBoot=default,DFUOnBoot=default,UploadMode=default,CPUFreq=240,FlashMode=qio,FlashSize=4M,PartitionScheme=default,DebugLevel=none,PSRAM=disabled,LoopCore=1,EventsCore=1,EraseFlash=none,JTAGAdapter=default,ZigbeeMode=default\"", "-DESP32=ESP32", "-DCORE_DEBUG_LEVEL=0", "-DARDUINO_RUNNING_CORE=1", "-DARDUINO_EVENT_RUNNING_CORE=1", "-DARDUINO_USB_MODE=1", "-DARDUINO_USB_CDC_ON_BOOT=0", "-DARDUINO_USB_MSC_ON_BOOT=0", "-DARDUINO_USB_DFU_ON_BOOT=0", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/defines", "-I/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta", "-iprefix", "/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/includes", "-I/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/qio_qspi/include", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/variants/esp32s3", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/build_opt.h", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/file_opts", "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/libb64/cdecode.c", "-o", "/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/core/libb64/cdecode.c.o"], "file": "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/libb64/cdecode.c"}, {"directory": "/home/<USER>", "arguments": ["/home/<USER>/.arduino15/packages/esp32/tools/esp-x32/2411/bin/xtensa-esp32s3-elf-gcc", "-M<PERSON>", "-c", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/c_flags", "-w", "-<PERSON><PERSON>", "-Werror=return-type", "-DF_CPU=240000000L", "-DARDUINO=10607", "-DARDUINO_ESP32S3_DEV", "-DARDUINO_ARCH_ESP32", "-DARDUINO_BOARD=\"ESP32S3_DEV\"", "-DARDUINO_VARIANT=\"esp32s3\"", "-DARDUINO_PARTITION_default", "-DARDUINO_HOST_OS=\"linux\"", "-DARDUINO_FQBN=\"esp32:esp32:esp32s3:UploadSpeed=921600,USBMode=hwcdc,CDCOnBoot=default,MSCOnBoot=default,DFUOnBoot=default,UploadMode=default,CPUFreq=240,FlashMode=qio,FlashSize=4M,PartitionScheme=default,DebugLevel=none,PSRAM=disabled,LoopCore=1,EventsCore=1,EraseFlash=none,JTAGAdapter=default,ZigbeeMode=default\"", "-DESP32=ESP32", "-DCORE_DEBUG_LEVEL=0", "-DARDUINO_RUNNING_CORE=1", "-DARDUINO_EVENT_RUNNING_CORE=1", "-DARDUINO_USB_MODE=1", "-DARDUINO_USB_CDC_ON_BOOT=0", "-DARDUINO_USB_MSC_ON_BOOT=0", "-DARDUINO_USB_DFU_ON_BOOT=0", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/defines", "-I/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta", "-iprefix", "/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/includes", "-I/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/qio_qspi/include", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/variants/esp32s3", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/build_opt.h", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/file_opts", "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/libb64/cencode.c", "-o", "/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/core/libb64/cencode.c.o"], "file": "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/libb64/cencode.c"}, {"directory": "/home/<USER>", "arguments": ["/home/<USER>/.arduino15/packages/esp32/tools/esp-x32/2411/bin/xtensa-esp32s3-elf-g++", "-M<PERSON>", "-c", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/cpp_flags", "-w", "-<PERSON><PERSON>", "-Werror=return-type", "-DF_CPU=240000000L", "-DARDUINO=10607", "-DARDUINO_ESP32S3_DEV", "-DARDUINO_ARCH_ESP32", "-DARDUINO_BOARD=\"ESP32S3_DEV\"", "-DARDUINO_VARIANT=\"esp32s3\"", "-DARDUINO_PARTITION_default", "-DARDUINO_HOST_OS=\"linux\"", "-DARDUINO_FQBN=\"esp32:esp32:esp32s3:UploadSpeed=921600,USBMode=hwcdc,CDCOnBoot=default,MSCOnBoot=default,DFUOnBoot=default,UploadMode=default,CPUFreq=240,FlashMode=qio,FlashSize=4M,PartitionScheme=default,DebugLevel=none,PSRAM=disabled,LoopCore=1,EventsCore=1,EraseFlash=none,JTAGAdapter=default,ZigbeeMode=default\"", "-DESP32=ESP32", "-DCORE_DEBUG_LEVEL=0", "-DARDUINO_RUNNING_CORE=1", "-DARDUINO_EVENT_RUNNING_CORE=1", "-DARDUINO_USB_MODE=1", "-DARDUINO_USB_CDC_ON_BOOT=0", "-DARDUINO_USB_MSC_ON_BOOT=0", "-DARDUINO_USB_DFU_ON_BOOT=0", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/defines", "-I/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta", "-iprefix", "/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/includes", "-I/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/qio_qspi/include", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/variants/esp32s3", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/build_opt.h", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/file_opts", "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/main.cpp", "-o", "/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/core/main.cpp.o"], "file": "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/main.cpp"}, {"directory": "/home/<USER>", "arguments": ["/home/<USER>/.arduino15/packages/esp32/tools/esp-x32/2411/bin/xtensa-esp32s3-elf-gcc", "-M<PERSON>", "-c", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/c_flags", "-w", "-<PERSON><PERSON>", "-Werror=return-type", "-DF_CPU=240000000L", "-DARDUINO=10607", "-DARDUINO_ESP32S3_DEV", "-DARDUINO_ARCH_ESP32", "-DARDUINO_BOARD=\"ESP32S3_DEV\"", "-DARDUINO_VARIANT=\"esp32s3\"", "-DARDUINO_PARTITION_default", "-DARDUINO_HOST_OS=\"linux\"", "-DARDUINO_FQBN=\"esp32:esp32:esp32s3:UploadSpeed=921600,USBMode=hwcdc,CDCOnBoot=default,MSCOnBoot=default,DFUOnBoot=default,UploadMode=default,CPUFreq=240,FlashMode=qio,FlashSize=4M,PartitionScheme=default,DebugLevel=none,PSRAM=disabled,LoopCore=1,EventsCore=1,EraseFlash=none,JTAGAdapter=default,ZigbeeMode=default\"", "-DESP32=ESP32", "-DCORE_DEBUG_LEVEL=0", "-DARDUINO_RUNNING_CORE=1", "-DARDUINO_EVENT_RUNNING_CORE=1", "-DARDUINO_USB_MODE=1", "-DARDUINO_USB_CDC_ON_BOOT=0", "-DARDUINO_USB_MSC_ON_BOOT=0", "-DARDUINO_USB_DFU_ON_BOOT=0", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/defines", "-I/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta", "-iprefix", "/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/includes", "-I/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/qio_qspi/include", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/variants/esp32s3", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/build_opt.h", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/file_opts", "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/stdlib_noniso.c", "-o", "/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/core/stdlib_noniso.c.o"], "file": "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/stdlib_noniso.c"}, {"directory": "/home/<USER>", "arguments": ["/home/<USER>/.arduino15/packages/esp32/tools/esp-x32/2411/bin/xtensa-esp32s3-elf-gcc", "-M<PERSON>", "-c", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/c_flags", "-w", "-<PERSON><PERSON>", "-Werror=return-type", "-DF_CPU=240000000L", "-DARDUINO=10607", "-DARDUINO_ESP32S3_DEV", "-DARDUINO_ARCH_ESP32", "-DARDUINO_BOARD=\"ESP32S3_DEV\"", "-DARDUINO_VARIANT=\"esp32s3\"", "-DARDUINO_PARTITION_default", "-DARDUINO_HOST_OS=\"linux\"", "-DARDUINO_FQBN=\"esp32:esp32:esp32s3:UploadSpeed=921600,USBMode=hwcdc,CDCOnBoot=default,MSCOnBoot=default,DFUOnBoot=default,UploadMode=default,CPUFreq=240,FlashMode=qio,FlashSize=4M,PartitionScheme=default,DebugLevel=none,PSRAM=disabled,LoopCore=1,EventsCore=1,EraseFlash=none,JTAGAdapter=default,ZigbeeMode=default\"", "-DESP32=ESP32", "-DCORE_DEBUG_LEVEL=0", "-DARDUINO_RUNNING_CORE=1", "-DARDUINO_EVENT_RUNNING_CORE=1", "-DARDUINO_USB_MODE=1", "-DARDUINO_USB_CDC_ON_BOOT=0", "-DARDUINO_USB_MSC_ON_BOOT=0", "-DARDUINO_USB_DFU_ON_BOOT=0", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/defines", "-I/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta", "-iprefix", "/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/includes", "-I/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/qio_qspi/include", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/variants/esp32s3", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/build_opt.h", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/file_opts", "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/wiring_pulse.c", "-o", "/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/core/wiring_pulse.c.o"], "file": "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/wiring_pulse.c"}, {"directory": "/home/<USER>", "arguments": ["/home/<USER>/.arduino15/packages/esp32/tools/esp-x32/2411/bin/xtensa-esp32s3-elf-gcc", "-M<PERSON>", "-c", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/c_flags", "-w", "-<PERSON><PERSON>", "-Werror=return-type", "-DF_CPU=240000000L", "-DARDUINO=10607", "-DARDUINO_ESP32S3_DEV", "-DARDUINO_ARCH_ESP32", "-DARDUINO_BOARD=\"ESP32S3_DEV\"", "-DARDUINO_VARIANT=\"esp32s3\"", "-DARDUINO_PARTITION_default", "-DARDUINO_HOST_OS=\"linux\"", "-DARDUINO_FQBN=\"esp32:esp32:esp32s3:UploadSpeed=921600,USBMode=hwcdc,CDCOnBoot=default,MSCOnBoot=default,DFUOnBoot=default,UploadMode=default,CPUFreq=240,FlashMode=qio,FlashSize=4M,PartitionScheme=default,DebugLevel=none,PSRAM=disabled,LoopCore=1,EventsCore=1,EraseFlash=none,JTAGAdapter=default,ZigbeeMode=default\"", "-DESP32=ESP32", "-DCORE_DEBUG_LEVEL=0", "-DARDUINO_RUNNING_CORE=1", "-DARDUINO_EVENT_RUNNING_CORE=1", "-DARDUINO_USB_MODE=1", "-DARDUINO_USB_CDC_ON_BOOT=0", "-DARDUINO_USB_MSC_ON_BOOT=0", "-DARDUINO_USB_DFU_ON_BOOT=0", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/defines", "-I/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta", "-iprefix", "/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/include/", "@/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/flags/includes", "-I/home/<USER>/.arduino15/packages/esp32/tools/esp32-arduino-libs/idf-release_v5.4-2f7dcd86-v1/esp32s3/qio_qspi/include", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32", "-I/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/variants/esp32s3", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/build_opt.h", "@/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/file_opts", "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/wiring_shift.c", "-o", "/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/build/core/wiring_shift.c.o"], "file": "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32/wiring_shift.c"}]