#line 1 "/home/<USER>/Downloads/morphing_clock1_Ubuntu/esp32_vbz_eta/.history/build/build_opt_20250731111536.h"
// Build options to work around AutoConnect library compatibility issues
#define AUTOCONNECT_USE_SPIFFS
#define AUTOCONNECT_USE_LITTLEFS
// Add permissive compilation flags
#pragma GCC diagnostic ignored "-Wunused-parameter"
#pragma GCC diagnostic ignored "-Wunused-variable"
#pragma GCC diagnostic ignored "-fpermissive"