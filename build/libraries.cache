["/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/cores/esp32", "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/variants/esp32s3", "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Wire/src", "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/SPI/src", "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WiFi/src", "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/Network/src", "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/WebServer/src", "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/FS/src", "/home/<USER>/Documents/Arduino/libraries/NTPClient", "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/SPIFFS/src", "/home/<USER>/Documents/Arduino/libraries/ArduinoJson/src", "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/DNSServer/src", "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/AsyncUDP/src", "/home/<USER>/Documents/Arduino/libraries/Adafruit_GFX_Library", "/home/<USER>/Documents/Arduino/libraries/Adafruit_BusIO", "/home/<USER>/Documents/Arduino/libraries/ESP32_HUB75_LED_MATRIX_PANEL_DMA_Display/src", "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/HTTPClient/src", "/home/<USER>/.arduino15/packages/esp32/hardware/esp32/3.2.0/libraries/NetworkClientSecure/src", "/home/<USER>/Documents/Arduino/libraries/UnixTime/src"]