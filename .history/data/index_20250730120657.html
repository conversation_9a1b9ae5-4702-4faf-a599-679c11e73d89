<!DOCTYPE html>
<html>
<head>
    <title>VBZ ETA Display Control</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        body {
            background-color: #121212;
            color: white;
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            display: flex;
            flex-direction: column;
            align-items: center;
            user-select: none;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
        }

        .header h1 {
            color: #ffff00;
            margin-bottom: 10px;
        }

        .header p {
            color: #aaa;
            margin: 5px 0;
        }

        .status-section {
            background-color: #333;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            width: 350px;
            text-align: center;
        }

        .status-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
            margin-bottom: 15px;
        }

        .status-item {
            background-color: #222;
            padding: 10px;
            border-radius: 5px;
        }

        .status-label {
            font-size: 12px;
            color: #aaa;
            margin-bottom: 5px;
        }

        .status-value {
            font-size: 16px;
            font-weight: bold;
        }

        .wifi-status { color: #00ff00; }
        .api-status { color: #00ffff; }
        .brightness { color: #ffff00; }
        .update-time { color: #ff00ff; }

        .controls-section {
            background-color: #333;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            width: 350px;
        }

        .control-group {
            margin-bottom: 20px;
        }

        .control-group h3 {
            color: #ffff00;
            margin-bottom: 10px;
            font-size: 16px;
        }

        .button-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 10px;
        }

        .btn {
            background-color: #444;
            border: none;
            color: white;
            padding: 12px;
            cursor: pointer;
            border-radius: 5px;
            transition: background-color 0.1s;
            touch-action: manipulation;
            font-size: 14px;
        }

        .btn:hover {
            background-color: #555;
        }

        .btn:active {
            background-color: #00ff00;
            color: black;
        }

        .brightness-control {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .brightness-slider {
            flex: 1;
            -webkit-appearance: none;
            appearance: none;
            height: 5px;
            background: #444;
            outline: none;
            border-radius: 5px;
        }

        .brightness-slider::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 20px;
            height: 20px;
            background: #ffff00;
            cursor: pointer;
            border-radius: 50%;
        }

        .api-controls {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .input-group {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }

        .input-group label {
            font-size: 12px;
            color: #aaa;
        }

        .input-group input, .input-group select {
            background-color: #222;
            border: 1px solid #444;
            color: white;
            padding: 8px;
            border-radius: 3px;
            font-size: 14px;
        }

        .connection-status {
            margin-top: 20px;
            padding: 10px;
            border-radius: 5px;
            text-align: center;
            width: 350px;
        }

        .connected {
            background-color: #004400;
            color: #00ff00;
        }

        .disconnected {
            background-color: #440000;
            color: #ff4444;
        }

        .info-section {
            margin-top: 20px;
            font-size: 14px;
            color: #aaa;
            text-align: center;
            max-width: 350px;
            background-color: #333;
            padding: 15px;
            border-radius: 10px;
        }

        .full-width {
            grid-column: span 2;
        }

        .transport-lines {
            background-color: #222;
            padding: 10px;
            border-radius: 5px;
            margin-top: 10px;
            max-height: 150px;
            overflow-y: auto;
        }

        .line-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 5px 0;
            border-bottom: 1px solid #444;
        }

        .line-item:last-child {
            border-bottom: none;
        }

        .line-number {
            background-color: #444;
            color: white;
            padding: 2px 8px;
            border-radius: 3px;
            font-weight: bold;
            min-width: 30px;
            text-align: center;
        }

        .line-destination {
            flex: 1;
            margin: 0 10px;
            font-size: 12px;
        }

        .line-time {
            color: #00ff00;
            font-weight: bold;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🚋 VBZ ETA Display</h1>
        <p>Zürich Public Transport Information</p>
        <p id="station-name">Station: Zürich Bellevue</p>
    </div>

    <div class="status-section">
        <h3 style="color: #ffff00; margin-bottom: 15px;">System Status</h3>
        <div class="status-grid">
            <div class="status-item">
                <div class="status-label">WIFI</div>
                <div id="wifi-status" class="status-value wifi-status">Connected</div>
            </div>
            <div class="status-item">
                <div class="status-label">API</div>
                <div id="api-status" class="status-value api-status">Active</div>
            </div>
            <div class="status-item">
                <div class="status-label">BRIGHTNESS</div>
                <div id="brightness-value" class="status-value brightness">64</div>
            </div>
            <div class="status-item">
                <div class="status-label">LAST UPDATE</div>
                <div id="last-update" class="status-value update-time">--:--</div>
            </div>
        </div>

        <div class="transport-lines" id="transport-lines">
            <div class="line-item">
                <div class="line-number">--</div>
                <div class="line-destination">Loading departures...</div>
                <div class="line-time">--</div>
            </div>
        </div>
    </div>

    <div class="controls-section">
        <div class="control-group">
            <h3>Display Controls</h3>
            <div class="button-grid">
                <button class="btn" id="refresh-data">🔄 Refresh Data</button>
                <button class="btn" id="toggle-display">💡 Toggle Display</button>
                <button class="btn" id="show-ip">🌐 Show IP</button>
                <button class="btn" id="show-splash">🏠 Show Splash</button>
            </div>
        </div>

        <div class="control-group">
            <h3>Brightness Control</h3>
            <div class="brightness-control">
                <span>🔅</span>
                <input type="range" id="brightness-slider" class="brightness-slider" 
                       min="12" max="255" value="64">
                <span>🔆</span>
                <span id="brightness-display">64</span>
            </div>
        </div>

        <div class="control-group">
            <h3>Station Settings</h3>
            <div class="api-controls">
                <div class="input-group">
                    <label for="station-id">Station ID (BPUIC)</label>
                    <input type="text" id="station-id" placeholder="8576193" value="8576193">
                </div>
                <div class="input-group">
                    <label for="direction">Direction</label>
                    <select id="direction">
                        <option value="A">All Directions</option>
                        <option value="H">Outward (H)</option>
                        <option value="R">Return (R)</option>
                    </select>
                </div>
                <div class="input-group">
                    <label for="max-results">Max Results</label>
                    <select id="max-results">
                        <option value="3">3 Results</option>
                        <option value="5" selected>5 Results</option>
                        <option value="8">8 Results</option>
                        <option value="10">10 Results</option>
                    </select>
                </div>
                <button class="btn full-width" id="apply-settings">💾 Apply Settings</button>
            </div>
        </div>
    </div>

    <div class="connection-status disconnected" id="connection-status">
        Connecting...
    </div>

    <div class="info-section">
        <strong>VBZ ETA Display Control</strong><br>
        Control your LED matrix display showing real-time departure information for Zürich public transport.<br><br>
        <strong>Features:</strong><br>
        • Live departure times with delay information<br>
        • Accessibility indicators for wheelchair access<br>
        • Automatic brightness adjustment<br>
        • Configurable station and direction filtering
    </div>

    <script>
        let systemStatus = {
            wifi: false,
            api: false,
            brightness: 64,
            lastUpdate: '',
            departures: []
        };

        // Update status display
        function updateStatusDisplay() {
            document.getElementById('wifi-status').textContent = systemStatus.wifi ? 'Connected' : 'Disconnected';
            document.getElementById('api-status').textContent = systemStatus.api ? 'Active' : 'Error';
            document.getElementById('brightness-value').textContent = systemStatus.brightness;
            document.getElementById('last-update').textContent = systemStatus.lastUpdate;

            // Update brightness slider
            document.getElementById('brightness-slider').value = systemStatus.brightness;
            document.getElementById('brightness-display').textContent = systemStatus.brightness;

            // Update departures
            updateDeparturesDisplay();
        }

        function updateDeparturesDisplay() {
            const container = document.getElementById('transport-lines');
            
            if (systemStatus.departures.length === 0) {
                container.innerHTML = `
                    <div class="line-item">
                        <div class="line-number">--</div>
                        <div class="line-destination">No departures available</div>
                        <div class="line-time">--</div>
                    </div>
                `;
                return;
            }

            container.innerHTML = systemStatus.departures.map(dep => `
                <div class="line-item">
                    <div class="line-number">${dep.line}</div>
                    <div class="line-destination">${dep.destination}${dep.accessible ? ' ♿' : ''}</div>
                    <div class="line-time">${dep.time}${dep.live ? '′' : '′'}</div>
                </div>
            `).join('');
        }

        // Send command to ESP32
        async function sendCommand(endpoint, data = null) {
            try {
                const options = {
                    method: data ? 'POST' : 'GET',
                    headers: data ? {'Content-Type': 'application/json'} : {}
                };
                
                if (data) {
                    options.body = JSON.stringify(data);
                }

                const response = await fetch('/' + endpoint, options);
                
                if (response.ok) {
                    document.getElementById('connection-status').className = 'connection-status connected';
                    document.getElementById('connection-status').textContent = 'Connected';
                    
                    if (response.headers.get('content-type')?.includes('application/json')) {
                        return await response.json();
                    }
                    return await response.text();
                } else {
                    throw new Error(`Command failed: ${response.status}`);
                }
            } catch (error) {
                document.getElementById('connection-status').className = 'connection-status disconnected';
                document.getElementById('connection-status').textContent = 'Connection Error';
                console.error('Error:', error);
                throw error;
            }
        }

        // Get system status from ESP32
        async function getSystemStatus() {
            try {
                const status = await sendCommand('status');
                if (typeof status === 'object') {
                    systemStatus = { ...systemStatus, ...status };
                    updateStatusDisplay();
                }
            } catch (error) {
                console.error('Failed to get status:', error);
            }
        }

        // Button event listeners
        document.getElementById('refresh-data').addEventListener('click', () => {
            sendCommand('refresh').then(() => {
                setTimeout(getSystemStatus, 1000); // Update status after refresh
            });
        });

        document.getElementById('toggle-display').addEventListener('click', () => {
            sendCommand('toggle');
        });

        document.getElementById('show-ip').addEventListener('click', () => {
            sendCommand('show-ip');
        });

        document.getElementById('show-splash').addEventListener('click', () => {
            sendCommand('splash');
        });

        // Brightness control
        document.getElementById('brightness-slider').addEventListener('input', (e) => {
            const brightness = e.target.value;
            document.getElementById('brightness-display').textContent = brightness;
            sendCommand('brightness', { value: parseInt(brightness) });
        });

        // Settings control
        document.getElementById('apply-settings').addEventListener('click', () => {
            const settings = {
                station: document.getElementById('station-id').value,
                direction: document.getElementById('direction').value,
                results: document.getElementById('max-results').value
            };
            
            sendCommand('settings', settings).then(() => {
                alert('Settings applied! The display will refresh with new data.');
                setTimeout(getSystemStatus, 2000);
            });
        });

        // Touch events for mobile
        function addTouchEvents(elementId, handler) {
            const element = document.getElementById(elementId);
            if (element) {
                element.addEventListener('touchstart', (e) => {
                    e.preventDefault();
                    handler();
                });
            }
        }

        // Add touch events for all buttons
        ['refresh-data', 'toggle-display', 'show-ip', 'show-splash', 'apply-settings'].forEach(id => {
            addTouchEvents(id, () => document.getElementById(id).click());
        });

        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            switch(e.key.toLowerCase()) {
                case 'r':
                    if (e.ctrlKey || e.metaKey) return; // Don't interfere with browser refresh
                    e.preventDefault();
                    document.getElementById('refresh-data').click();
                    break;
                case 't':
                    e.preventDefault();
                    document.getElementById('toggle-display').click();
                    break;
                case 'i':
                    e.preventDefault();
                    document.getElementById('show-ip').click();
                    break;
                case 's':
                    e.preventDefault();
                    document.getElementById('show-splash').click();
                    break;
            }
        });

        // Update status every 2 seconds
        setInterval(getSystemStatus, 2000);

        // Initial status check
        getSystemStatus();
    </script>
</body>
</html>