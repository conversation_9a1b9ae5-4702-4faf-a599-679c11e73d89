#ifndef CONFIG_H
#define CONFIG_H

#define TIME_SERVER "0.ch.pool.ntp.org"
#define AP_NAME "vbz-anzeige"
#define AP_PASSWORD "123456"

// Api
#define OPEN_DATA_API_KEY "XXXXX"
#define OPEN_DATA_URL "https://api.opentransportdata.swiss/trias2020"
#define OPEN_DATA_STATION "8576193" // Zürich Bellevue
#define OPEN_DATA_RESULTS "5"
#define OPEN_DATA_DIRECTION "A" // H = Outward, R = Return, A = All

// 🔌 Pin Definitions (ESP32-S3 to HUB75 RGB Matrix)
// ---------------------------
#define R1_PIN 47
#define G1_PIN 1
#define B1_PIN 48
#define R2_PIN 45
#define G2_PIN 2
#define B2_PIN 0  // ⚠️ GPIO0 is BOOT pin, must stay HIGH at boot

#define A_PIN 35
#define B_PIN 41
#define C_PIN 36
#define D_PIN 40
#define E_PIN 42

#define LAT_PIN 39
#define OE_PIN 38
#define CLK_PIN 37
#define PANEL_RES_X 128 // Number of pixels wide of each INDIVIDUAL panel module.
#define PANEL_RES_Y 64  // Number of pixels tall of each INDIVIDUAL panel module.
#define PANEL_CHAIN 1   // Total number of panels chained one to another

#endif