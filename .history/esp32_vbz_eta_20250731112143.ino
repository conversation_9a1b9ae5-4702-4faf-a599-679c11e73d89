#include "Config.h"
#include <Arduino.h>
#include <Wire.h>
#include <SPI.h>
#include <WiFi.h>
#include <WebServer.h>
#include <NTPClient.h>
#include <SPIFFS.h>
#include <ArduinoJson.h>
#include <DNSServer.h>

#include "Display.h"
#include "OpenTransportDataSwiss.h"

// Set offset time in seconds to adjust for your timezone, for example:
// GMT +1 = 3600
// GMT +8 = 28800
// GMT -1 = -3600
// GMT 0 = 0
int timeOffset = 3600;
const char timeServer[] = TIME_SERVER;
int sensorValue;
int loopCounter = 0;
bool displayEnabled = true;
unsigned long lastApiCall = 0;
const unsigned long API_CALL_INTERVAL = 32000; // 32 seconds

OpenTransportDataSwiss api(
    OPEN_DATA_STATION,
    OPEN_DATA_DIRECTION,
    OPEN_DATA_URL,
    OPEN_DATA_API_KEY,
    OPEN_DATA_RESULTS);

Display display;

WebServer Server;
DNSServer dnsServer;

// Define NTP Client to get time
WiFiUDP ntpUDP;
NTPClient timeClient(ntpUDP, timeServer, timeOffset, 60000);
WiFiClient client;

// WiFi connection variables
bool isAPMode = false;
unsigned long wifiConnectStart = 0;

// Web server handlers
void handleRoot() {
  // If in AP mode and no index.html, show WiFi config page
  if (isAPMode) {
    File file = SPIFFS.open("/index.html", "r");
    if(!file) {
      String html = "<!DOCTYPE html><html><head><title>VBZ Display Setup</title></head><body>";
      html += "<h1>VBZ Display Setup</h1>";
      html += "<p>Welcome to the VBZ Display configuration portal.</p>";
      html += "<h2>WiFi Configuration</h2>";
      html += "<p>Please configure your WiFi connection:</p>";
      html += "<a href='/wifi-config'><button>Configure WiFi</button></a>";
      html += "<h2>Device Status</h2>";
      html += "<p>Current mode: Access Point</p>";
      html += "<p>AP IP: " + WiFi.softAPIP().toString() + "</p>";
      html += "<br><a href='/status'>View Status</a> | ";
      html += "<a href='/refresh'>Refresh Data</a> | ";
      html += "<a href='/toggle'>Toggle Display</a>";
      html += "</body></html>";
      Server.send(200, "text/html", html);
      return;
    }
    file.close();
  }

  // Serve the normal index.html file
  File file = SPIFFS.open("/index.html", "r");
  if(file){
    Server.streamFile(file, "text/html");
    file.close();
  } else {
    Server.send(404, "text/plain", "index.html not found in SPIFFS");
  }
}

void handleStatus() {
  StaticJsonDocument<512> status;
  status["wifi"] = WiFi.status() == WL_CONNECTED;
  status["api"] = (api.httpLastError == "");
  status["brightness"] = sensorValue;
  status["lastUpdate"] = timeClient.getFormattedTime();
  status["displayEnabled"] = displayEnabled;
  
  // Add departure information
  JsonArray departures = status.createNestedArray("departures");
  if (!api.doc.isNull()) {
    JsonArray data = api.doc.as<JsonArray>();
    for (const auto &value : data) {
      if (value["ttl"].as<int>() >= 0) {
        JsonObject dep = departures.createNestedObject();
        dep["line"] = value["line"].as<String>();
        dep["destination"] = value["destination"].as<String>();
        dep["time"] = value["ttl"].as<int>();
        dep["live"] = value["liveData"].as<bool>();
        dep["accessible"] = value["isNF"].as<bool>();
      }
    }
  }
  
  String response;
  serializeJson(status, response);
  Server.send(200, "application/json", response);
}

void handleRefresh() {
  if (int apiCode = api.getWebData(timeClient) == 0) {
    if (displayEnabled) {
      display.printLines(api.doc.as<JsonArray>());
    }
    Server.send(200, "text/plain", "Data refreshed");
  } else {
    Server.send(500, "text/plain", "Failed to refresh data: " + api.httpLastError);
  }
}

void handleToggleDisplay() {
  displayEnabled = !displayEnabled;
  if (!displayEnabled) {
    display.printError("Display disabled via web interface");
  } else {
    // Refresh display with current data
    if (!api.doc.isNull()) {
      display.printLines(api.doc.as<JsonArray>());
    }
  }
  Server.send(200, "text/plain", displayEnabled ? "Display enabled" : "Display disabled");
}

void handleShowIP() {
  display.showIpAddress(WiFi.localIP().toString().c_str());
  Server.send(200, "text/plain", "Showing IP address on display");
}

void handleShowSplash() {
  display.showSplash();
  Server.send(200, "text/plain", "Showing splash screen");
}

void handleBrightness() {
  if (Server.hasArg("plain")) {
    StaticJsonDocument<64> doc;
    deserializeJson(doc, Server.arg("plain"));
    int brightness = doc["value"] | 64;
    brightness = constrain(brightness, 12, 255);
    display.displaySetBrightness(brightness);
    sensorValue = brightness; // Override sensor value
    Server.send(200, "text/plain", "Brightness set to " + String(brightness));
  } else {
    Server.send(400, "text/plain", "Missing brightness value");
  }
}

void handleSettings() {
  if (Server.hasArg("plain")) {
    StaticJsonDocument<256> doc;
    deserializeJson(doc, Server.arg("plain"));
    
    String station = doc["station"] | OPEN_DATA_STATION;
    String direction = doc["direction"] | OPEN_DATA_DIRECTION;
    String results = doc["results"] | OPEN_DATA_RESULTS;
    
    // Update API settings
    api.stopPointBPUIC = station;
    api.direction = direction;
    api.numResultsString = results;
    
    Server.send(200, "text/plain", "Settings updated");
  } else {
    Server.send(400, "text/plain", "Missing settings data");
  }
}

void handleWiFiConfig() {
  String html = "<!DOCTYPE html><html><head><title>WiFi Configuration</title></head><body>";
  html += "<h1>WiFi Configuration</h1>";
  html += "<form action='/wifi-save' method='post'>";
  html += "SSID: <input type='text' name='ssid' required><br><br>";
  html += "Password: <input type='password' name='password'><br><br>";
  html += "<input type='submit' value='Save and Connect'>";
  html += "</form>";
  html += "<br><a href='/'>Back to Main Page</a>";
  html += "</body></html>";
  Server.send(200, "text/html", html);
}

void handleWiFiSave() {
  if (Server.hasArg("ssid")) {
    String ssid = Server.arg("ssid");
    String password = Server.arg("password");

    Server.send(200, "text/html",
      "<!DOCTYPE html><html><head><title>Connecting...</title></head><body>"
      "<h1>Connecting to WiFi...</h1>"
      "<p>Attempting to connect to: " + ssid + "</p>"
      "<p>Please wait...</p>"
      "<script>setTimeout(function(){window.location.href='/';}, 10000);</script>"
      "</body></html>");

    delay(1000);

    // Try to connect with new credentials
    WiFi.mode(WIFI_STA);
    WiFi.begin(ssid.c_str(), password.c_str());

    unsigned long connectStart = millis();
    while (WiFi.status() != WL_CONNECTED && (millis() - connectStart) < WIFI_TIMEOUT) {
      delay(500);
    }

    if (WiFi.status() == WL_CONNECTED) {
      isAPMode = false;
      dnsServer.stop();
#ifdef DEBUG
      Serial.println("WiFi connected with new credentials!");
      Serial.print("IP address: ");
      Serial.println(WiFi.localIP());
#endif
      display.showIpAddress(WiFi.localIP().toString().c_str());
    } else {
      // Connection failed, go back to AP mode
      startAccessPoint();
    }
  } else {
    Server.send(400, "text/plain", "Missing SSID");
  }
}

bool connectToWiFi() {
  WiFi.mode(WIFI_STA);
  WiFi.begin(WIFI_SSID, WIFI_PASSWORD);

  display.connectingMsg();

#ifdef DEBUG
  Serial.print("Connecting to WiFi");
#endif

  wifiConnectStart = millis();
  while (WiFi.status() != WL_CONNECTED && (millis() - wifiConnectStart) < WIFI_TIMEOUT) {
    delay(500);
#ifdef DEBUG
    Serial.print(".");
#endif
  }

  if (WiFi.status() == WL_CONNECTED) {
#ifdef DEBUG
    Serial.println();
    Serial.println("WiFi connected!");
    Serial.print("IP address: ");
    Serial.println(WiFi.localIP());
#endif
    return true;
  } else {
#ifdef DEBUG
    Serial.println();
    Serial.println("WiFi connection failed, starting AP mode");
#endif
    return false;
  }
}

void startAccessPoint() {
  WiFi.mode(WIFI_AP);
  WiFi.softAP(AP_NAME, AP_PASSWORD);

  IPAddress apIP = WiFi.softAPIP();
  isAPMode = true;

#ifdef DEBUG
  Serial.println("Access Point started");
  Serial.print("AP IP address: ");
  Serial.println(apIP);
#endif

  display.connectionMsg(AP_NAME, AP_PASSWORD);

  // Start DNS server for captive portal
  dnsServer.start(53, "*", apIP);
}

void setupWebServer() {
  // Initialize SPIFFS
  if(!SPIFFS.begin(true)){
    Serial.println("SPIFFS Mount Failed");
    return;
  }

  // List SPIFFS files for debugging
  Serial.println("SPIFFS files:");
  File root = SPIFFS.open("/");
  File file = root.openNextFile();
  while(file){
    Serial.print("FILE: ");
    Serial.println(file.name());
    file = root.openNextFile();
  }

  // Web server endpoints
  Server.on("/", HTTP_GET, handleRoot);
  Server.on("/status", HTTP_GET, handleStatus);
  Server.on("/refresh", HTTP_GET, handleRefresh);
  Server.on("/toggle", HTTP_GET, handleToggleDisplay);
  Server.on("/show-ip", HTTP_GET, handleShowIP);
  Server.on("/splash", HTTP_GET, handleShowSplash);
  Server.on("/brightness", HTTP_POST, handleBrightness);
  Server.on("/settings", HTTP_POST, handleSettings);
  Server.on("/wifi-config", HTTP_GET, handleWiFiConfig);
  Server.on("/wifi-save", HTTP_POST, handleWiFiSave);

  // Serve static files
  Server.serveStatic("/", SPIFFS, "/");

  // 404 handler
  Server.onNotFound([](){
    String message = "File Not Found\n\n";
    message += "URI: ";
    message += Server.uri();
    message += "\nMethod: ";
    message += (Server.method() == HTTP_GET) ? "GET" : "POST";
    Server.send(404, "text/plain", message);
  });

  Server.begin();
  Serial.println("Web server started");
}

void setup()
{
  delay(1000);

#ifdef DEBUG
  Serial.begin(MONITOR_SPEED);
  Serial.setDebugOutput(true);
  Serial.println("Debug Enabled");
#endif

  display.begin(
      R1_PIN,
      G1_PIN,
      B1_PIN,
      R2_PIN,
      G2_PIN,
      B2_PIN,
      A_PIN,
      B_PIN,
      C_PIN,
      D_PIN,
      E_PIN,
      LAT_PIN,
      OE_PIN,
      CLK_PIN,
      PANEL_RES_X,
      PANEL_RES_Y,
      PANEL_CHAIN);

  display.showSplash();

  delay(3000);

  // Try to connect to WiFi, fallback to AP mode if failed
  if (!connectToWiFi()) {
    startAccessPoint();
  }

  // Show IP address on display
  if (isAPMode) {
    display.showIpAddress(WiFi.softAPIP().toString().c_str());
  } else {
    display.showIpAddress(WiFi.localIP().toString().c_str());
  }
  delay(2000);

  // Initialize a NTPClient to get time
  timeClient.begin();
  timeClient.setTimeOffset(timeOffset);

  // Setup web server
  setupWebServer();
}

void loop()
{
  // Handle DNS requests in AP mode
  if (isAPMode) {
    dnsServer.processNextRequest();
  }

  Server.handleClient();

  int timeTimer = 0;
  while (!timeClient.update())
  {
    if (timeTimer > 30) {
      if (displayEnabled) {
        display.printError("Unable to get time\nfrom NTP Server:\n" + (String) TIME_SERVER);
      }
    }

    timeClient.forceUpdate();
    timeTimer++;
  }

#ifdef DEBUG
  Serial.println("Time: " + timeClient.getFormattedTime());
#endif

  // brightness sensor
  sensorValue = analogRead(A0);
  sensorValue = map(sensorValue, 0, 4095, 12, 255);
  display.displaySetBrightness(sensorValue);

  // API call with timing control
  unsigned long currentTime = millis();
  if (currentTime - lastApiCall >= API_CALL_INTERVAL || lastApiCall == 0)
  {
    if (int apiCode = api.getWebData(timeClient) == 0)
    {
      if (displayEnabled) {
        display.printLines(api.doc.as<JsonArray>());
      }
    }
    else
    {
      if (displayEnabled) {
        display.printError(api.httpLastError);
      }
    }
    lastApiCall = currentTime;
  }

  delay(1000);
}